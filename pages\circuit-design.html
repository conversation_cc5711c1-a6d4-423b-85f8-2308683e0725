<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Circuit Design - ECG Signal System</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        .circuit-container {
            padding: 100px 0 50px;
            background: #f8f9fa;
        }

        .circuit-stage {
            background: white;
            margin: 2rem 0;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stage-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .stage-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-right: 1rem;
        }

        .component-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .component-card {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .component-card h4 {
            color: #333;
            margin-bottom: 0.5rem;
        }

        .spec-list {
            list-style: none;
            padding: 0;
        }

        .spec-list li {
            padding: 0.25rem 0;
            color: #666;
            font-size: 0.9rem;
        }

        .interactive-circuit {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .circuit-controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .control-group label {
            font-weight: 600;
            color: #333;
        }

        .control-group input,
        .control-group select {
            padding: 0.5rem;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }

        .control-group input:focus,
        .control-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .frequency-response {
            height: 400px;
            margin: 1rem 0;
        }

        .schematic-viewer {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }

        .schematic-placeholder {
            font-size: 4rem;
            color: #ddd;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-heartbeat"></i>
                <span>ECG Simulator</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="../index.html" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="#circuit" class="nav-link active">Circuit Design</a>
                </li>
                <li class="nav-item">
                    <a href="signal-analysis.html" class="nav-link">Signal Analysis</a>
                </li>
                <li class="nav-item">
                    <a href="pcb-viewer.html" class="nav-link">PCB Viewer</a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Circuit Design Content -->
    <div class="circuit-container">
        <div class="container">
            <h1 class="section-title">ECG Circuit Design</h1>

            <!-- Interactive Circuit Simulator -->
            <div class="interactive-circuit">
                <h2>Interactive Circuit Simulator</h2>
                <div class="circuit-controls">
                    <div class="control-group">
                        <label for="inputSignal">Input Signal</label>
                        <select id="inputSignal">
                            <option value="ecg">Normal ECG</option>
                            <option value="sine">Sine Wave</option>
                            <option value="square">Square Wave</option>
                            <option value="noise">Noise</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label for="gain">System Gain</label>
                        <input type="range" id="gain" min="100" max="2000" value="1100">
                        <span id="gainValue">1100x</span>
                    </div>
                    <div class="control-group">
                        <label for="frequency">Frequency (Hz)</label>
                        <input type="range" id="frequency" min="0.1" max="100" value="1" step="0.1">
                        <span id="frequencyValue">1.0 Hz</span>
                    </div>
                    <div class="control-group">
                        <button type="button" class="btn btn-primary" onclick="updateSimulation()">
                            <i class="fas fa-play"></i> Run Simulation
                        </button>
                    </div>
                </div>

                <div class="frequency-response" id="frequencyResponse"></div>
            </div>

            <!-- Stage 1: Input Protection and Buffering -->
            <div class="circuit-stage">
                <div class="stage-header">
                    <div class="stage-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div>
                        <h2>Stage 1: Input Protection & Buffering</h2>
                        <p>Electrode interface and input protection circuitry</p>
                    </div>
                </div>

                <div class="component-grid">
                    <div class="component-card">
                        <h4>Input Protection</h4>
                        <ul class="spec-list">
                            <li>ESD Protection Diodes</li>
                            <li>Input Impedance: >10MΩ</li>
                            <li>Common Mode Range: ±10V</li>
                            <li>Overvoltage Protection</li>
                        </ul>
                    </div>
                    <div class="component-card">
                        <h4>Electrode Interface</h4>
                        <ul class="spec-list">
                            <li>Ag/AgCl Electrodes</li>
                            <li>Contact Impedance: <2kΩ</li>
                            <li>Offset Voltage: <100μV</li>
                            <li>3-Lead Configuration</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Stage 2: Instrumentation Amplifier -->
            <div class="circuit-stage">
                <div class="stage-header">
                    <div class="stage-icon">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </div>
                    <div>
                        <h2>Stage 2: Instrumentation Amplifier (INA128UA)</h2>
                        <p>High-precision differential amplification with excellent CMRR</p>
                    </div>
                </div>

                <div class="component-grid">
                    <div class="component-card">
                        <h4>INA128UA Specifications</h4>
                        <ul class="spec-list">
                            <li>Gain: 1 to 10,000 (Programmable)</li>
                            <li>CMRR: 120dB min</li>
                            <li>Input Offset: 50μV max</li>
                            <li>Input Bias Current: 5nA max</li>
                            <li>Bandwidth: 200kHz</li>
                        </ul>
                    </div>
                    <div class="component-card">
                        <h4>Gain Setting</h4>
                        <ul class="spec-list">
                            <li>Gain = 1 + (50kΩ/RG)</li>
                            <li>RG = 47Ω (for gain ≈ 1000)</li>
                            <li>Precision Metal Film Resistor</li>
                            <li>Temperature Coefficient: 25ppm/°C</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Stage 3: Filtering -->
            <div class="circuit-stage">
                <div class="stage-header">
                    <div class="stage-icon">
                        <i class="fas fa-filter"></i>
                    </div>
                    <div>
                        <h2>Stage 3: Analog Filtering</h2>
                        <p>Multi-stage filtering for noise reduction and signal conditioning</p>
                    </div>
                </div>

                <div class="component-grid">
                    <div class="component-card">
                        <h4>High-Pass Filter</h4>
                        <ul class="spec-list">
                            <li>Cutoff: 0.5Hz (-3dB)</li>
                            <li>Type: 2nd Order Butterworth</li>
                            <li>Purpose: DC offset removal</li>
                            <li>Implementation: Sallen-Key</li>
                        </ul>
                    </div>
                    <div class="component-card">
                        <h4>Low-Pass Filter</h4>
                        <ul class="spec-list">
                            <li>Cutoff: 150Hz (-3dB)</li>
                            <li>Type: 4th Order Butterworth</li>
                            <li>Purpose: Anti-aliasing</li>
                            <li>Op-Amp: OPA2131UA</li>
                        </ul>
                    </div>
                    <div class="component-card">
                        <h4>Notch Filter</h4>
                        <ul class="spec-list">
                            <li>Frequency: 50/60Hz</li>
                            <li>Q Factor: 30</li>
                            <li>Rejection: >40dB</li>
                            <li>Purpose: Power line interference</li>
                        </ul>
                    </div>
                    <div class="component-card">
                        <h4>Buffer Amplifier</h4>
                        <ul class="spec-list">
                            <li>Op-Amp: LM358</li>
                            <li>Gain: Unity (1x)</li>
                            <li>Purpose: Impedance matching</li>
                            <li>Output Drive: 10mA</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Stage 4: Output Stage -->
            <div class="circuit-stage">
                <div class="stage-header">
                    <div class="stage-icon">
                        <i class="fas fa-microchip"></i>
                    </div>
                    <div>
                        <h2>Stage 4: Output Stage & ADC Interface</h2>
                        <p>Level shifting and ADC interface for STM32F4</p>
                    </div>
                </div>

                <div class="component-grid">
                    <div class="component-card">
                        <h4>Level Shifter (OP07D)</h4>
                        <ul class="spec-list">
                            <li>Input Range: ±5.5V (after amplification)</li>
                            <li>Output Range: 0-3.3V</li>
                            <li>DC Offset: 1.65V (VCC/2)</li>
                            <li>Gain: Unity (1x)</li>
                            <li>Input Offset: <25μV</li>
                            <li>Bandwidth: 600kHz</li>
                        </ul>
                    </div>
                    <div class="component-card">
                        <h4>STM32F4 ADC Interface</h4>
                        <ul class="spec-list">
                            <li>Resolution: 12-bit (4096 levels)</li>
                            <li>Sample Rate: 256Hz (ECG)</li>
                            <li>Reference: 3.3V (VREF+)</li>
                            <li>Input Impedance: >1MΩ</li>
                            <li>Conversion Time: 1.17μs</li>
                            <li>DNL: ±1 LSB, INL: ±2 LSB</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Design Calculations -->
            <div class="circuit-stage">
                <div class="stage-header">
                    <div class="stage-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div>
                        <h2>Design Calculations & Analysis</h2>
                        <p>Key design equations and performance calculations</p>
                    </div>
                </div>

                <div class="component-grid">
                    <div class="component-card">
                        <h4>Gain Calculations</h4>
                        <ul class="spec-list">
                            <li><strong>INA128UA Gain:</strong> G = 1 + (50kΩ/RG)</li>
                            <li><strong>RG Value:</strong> 47Ω (for G ≈ 1064)</li>
                            <li><strong>Total System Gain:</strong> 1100x (60.8dB)</li>
                            <li><strong>Input Signal:</strong> 1mV ECG → 1.1V output</li>
                            <li><strong>Gain Accuracy:</strong> ±0.5% (1% resistor tolerance)</li>
                        </ul>
                    </div>
                    <div class="component-card">
                        <h4>Filter Calculations</h4>
                        <ul class="spec-list">
                            <li><strong>HPF Cutoff:</strong> fc = 1/(2π√(R×C)) = 0.5Hz</li>
                            <li><strong>LPF Cutoff:</strong> fc = 1/(2πRC) = 150Hz</li>
                            <li><strong>Notch Frequency:</strong> f0 = 1/(2πRC) = 50Hz</li>
                            <li><strong>Q Factor:</strong> Q = f0/BW = 30</li>
                            <li><strong>Phase Margin:</strong> >60° (stability)</li>
                        </ul>
                    </div>
                    <div class="component-card">
                        <h4>Noise Analysis</h4>
                        <ul class="spec-list">
                            <li><strong>INA128UA Noise:</strong> 8nV/√Hz @ 1kHz</li>
                            <li><strong>Thermal Noise (1kΩ):</strong> 4nV/√Hz</li>
                            <li><strong>Total Input Noise:</strong> <2μVrms (0.5-150Hz)</li>
                            <li><strong>SNR:</strong> 20×log(1mV/2μV) = 54dB</li>
                            <li><strong>Dynamic Range:</strong> 72dB (12-bit ADC)</li>
                        </ul>
                    </div>
                    <div class="component-card">
                        <h4>Power Budget</h4>
                        <ul class="spec-list">
                            <li><strong>INA128UA:</strong> 700μA × 18V = 12.6mW</li>
                            <li><strong>OPA2131UA:</strong> 900μA × 18V = 16.2mW</li>
                            <li><strong>LM358:</strong> 500μA × 18V = 9.0mW</li>
                            <li><strong>OP07D:</strong> 300μA × 18V = 5.4mW</li>
                            <li><strong>Total Power:</strong> 43.2mW</li>
                            <li><strong>Battery Life:</strong> 208 hours (9V, 500mAh)</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Performance Specifications -->
            <div class="circuit-stage">
                <div class="stage-header">
                    <div class="stage-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div>
                        <h2>Performance Specifications</h2>
                        <p>Measured and calculated system performance metrics</p>
                    </div>
                </div>

                <div class="component-grid">
                    <div class="component-card">
                        <h4>Frequency Response</h4>
                        <ul class="spec-list">
                            <li><strong>Passband:</strong> 0.5Hz - 150Hz (-3dB)</li>
                            <li><strong>Gain Flatness:</strong> ±0.5dB in passband</li>
                            <li><strong>High-freq Rolloff:</strong> -80dB/decade (>150Hz)</li>
                            <li><strong>Low-freq Rolloff:</strong> -40dB/decade (<0.5Hz)</li>
                            <li><strong>Group Delay:</strong> <10ms (linear phase)</li>
                        </ul>
                    </div>
                    <div class="component-card">
                        <h4>Distortion & Linearity</h4>
                        <ul class="spec-list">
                            <li><strong>THD:</strong> <0.1% @ 1mV input, 10Hz</li>
                            <li><strong>IMD:</strong> <0.05% (19kHz + 20kHz test)</li>
                            <li><strong>Linearity Error:</strong> <0.1% FSR</li>
                            <li><strong>Saturation Level:</strong> ±4.5V output</li>
                            <li><strong>Slew Rate Limit:</strong> 20V/μs (OPA2131UA)</li>
                        </ul>
                    </div>
                    <div class="component-card">
                        <h4>Common Mode Performance</h4>
                        <ul class="spec-list">
                            <li><strong>CMRR:</strong> >120dB @ DC, >80dB @ 50Hz</li>
                            <li><strong>CM Input Range:</strong> ±10V</li>
                            <li><strong>CM Rejection Ratio:</strong> 1,000,000:1</li>
                            <li><strong>Power Supply Rejection:</strong> >80dB</li>
                            <li><strong>Temperature Drift:</strong> <5μV/°C</li>
                        </ul>
                    </div>
                    <div class="component-card">
                        <h4>Digital Interface</h4>
                        <ul class="spec-list">
                            <li><strong>ADC Resolution:</strong> 12-bit (0.8mV/LSB)</li>
                            <li><strong>Effective Bits:</strong> 10.5 ENOB</li>
                            <li><strong>Sampling Jitter:</strong> <1ns RMS</li>
                            <li><strong>Aperture Delay:</strong> 7ns</li>
                            <li><strong>Digital Filter:</strong> 50Hz notch (software)</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Interactive Schematic Viewer -->
            <div class="interactive-circuit">
                <h2>Interactive Circuit Schematic</h2>
                <div class="circuit-controls">
                    <div class="control-group">
                        <label for="schematicView">View Mode</label>
                        <select id="schematicView" onchange="switchSchematicView()">
                            <option value="complete">Complete Circuit</option>
                            <option value="stage1">Stage 1: Input Protection</option>
                            <option value="stage2">Stage 2: Instrumentation Amp</option>
                            <option value="stage3">Stage 3: Filtering</option>
                            <option value="stage4">Stage 4: Output Stage</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <button type="button" class="btn btn-primary" onclick="openCircuitDesigner()">
                            <i class="fas fa-edit"></i> Open in Designer
                        </button>
                    </div>
                    <div class="control-group">
                        <button type="button" class="btn btn-secondary" onclick="exportSchematic()">
                            <i class="fas fa-download"></i> Export Schematic
                        </button>
                    </div>
                </div>
                <div class="schematic-viewer" id="schematicViewer">
                    <div class="schematic-content">
                        <svg width="100%" height="400" viewBox="0 0 800 400" id="circuitSVG">
                            <!-- Complete ECG Circuit Schematic -->
                            <defs>
                                <marker id="arrowhead" markerWidth="10" markerHeight="7"
                                        refX="9" refY="3.5" orient="auto">
                                    <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
                                </marker>
                            </defs>

                            <!-- Input Stage -->
                            <g id="inputStage">
                                <rect x="50" y="150" width="80" height="60" fill="#e8f5e8" stroke="#4caf50" stroke-width="2" rx="5"/>
                                <text x="90" y="175" text-anchor="middle" font-size="12" font-weight="bold">Input</text>
                                <text x="90" y="190" text-anchor="middle" font-size="10">Protection</text>
                                <circle cx="30" cy="170" r="5" fill="#ff6b6b"/>
                                <circle cx="30" cy="190" r="5" fill="#ff6b6b"/>
                                <text x="15" y="175" font-size="10">RA</text>
                                <text x="15" y="195" font-size="10">LA</text>
                                <line x1="35" y1="170" x2="50" y2="170" stroke="#333" stroke-width="2"/>
                                <line x1="35" y1="190" x2="50" y2="190" stroke="#333" stroke-width="2"/>
                            </g>

                            <!-- INA128UA Instrumentation Amplifier -->
                            <g id="inaStage">
                                <rect x="180" y="140" width="100" height="80" fill="#fff3e0" stroke="#ff9800" stroke-width="2" rx="5"/>
                                <text x="230" y="165" text-anchor="middle" font-size="12" font-weight="bold">INA128UA</text>
                                <text x="230" y="180" text-anchor="middle" font-size="10">Gain: 1100x</text>
                                <text x="230" y="195" text-anchor="middle" font-size="10">CMRR: 120dB</text>
                                <line x1="130" y1="180" x2="180" y2="180" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                                <text x="155" y="175" font-size="10">±1mV</text>
                            </g>

                            <!-- Filter Stage -->
                            <g id="filterStage">
                                <rect x="330" y="120" width="120" height="120" fill="#e3f2fd" stroke="#2196f3" stroke-width="2" rx="5"/>
                                <text x="390" y="145" text-anchor="middle" font-size="12" font-weight="bold">Analog Filters</text>
                                <text x="390" y="165" text-anchor="middle" font-size="10">HPF: 0.5Hz</text>
                                <text x="390" y="180" text-anchor="middle" font-size="10">LPF: 150Hz</text>
                                <text x="390" y="195" text-anchor="middle" font-size="10">Notch: 50Hz</text>
                                <text x="390" y="210" text-anchor="middle" font-size="10">OPA2131UA</text>
                                <line x1="280" y1="180" x2="330" y2="180" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                                <text x="305" y="175" font-size="10">±1.1V</text>
                            </g>

                            <!-- Output Stage -->
                            <g id="outputStage">
                                <rect x="500" y="140" width="100" height="80" fill="#fce4ec" stroke="#e91e63" stroke-width="2" rx="5"/>
                                <text x="550" y="165" text-anchor="middle" font-size="12" font-weight="bold">OP07D</text>
                                <text x="550" y="180" text-anchor="middle" font-size="10">Level Shift</text>
                                <text x="550" y="195" text-anchor="middle" font-size="10">0-3.3V</text>
                                <line x1="450" y1="180" x2="500" y2="180" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                            </g>

                            <!-- ADC Interface -->
                            <g id="adcStage">
                                <rect x="650" y="150" width="80" height="60" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2" rx="5"/>
                                <text x="690" y="175" text-anchor="middle" font-size="12" font-weight="bold">STM32F4</text>
                                <text x="690" y="190" text-anchor="middle" font-size="10">12-bit ADC</text>
                                <line x1="600" y1="180" x2="650" y2="180" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
                                <text x="625" y="175" font-size="10">0-3.3V</text>
                            </g>

                            <!-- Power Supply -->
                            <g id="powerSupply">
                                <rect x="300" y="50" width="120" height="40" fill="#fff8e1" stroke="#ffc107" stroke-width="2" rx="5"/>
                                <text x="360" y="75" text-anchor="middle" font-size="12" font-weight="bold">A0509S-1WR3</text>
                                <text x="360" y="35" text-anchor="middle" font-size="10">±9V → ±5V</text>

                                <!-- Power lines -->
                                <line x1="360" y1="90" x2="360" y2="120" stroke="#ffc107" stroke-width="2"/>
                                <line x1="200" y1="120" x2="600" y2="120" stroke="#ffc107" stroke-width="2"/>
                                <line x1="230" y1="120" x2="230" y2="140" stroke="#ffc107" stroke-width="1"/>
                                <line x1="390" y1="120" x2="390" y2="120" stroke="#ffc107" stroke-width="1"/>
                                <line x1="550" y1="120" x2="550" y2="140" stroke="#ffc107" stroke-width="1"/>
                            </g>

                            <!-- Ground -->
                            <g id="ground">
                                <line x1="100" y1="280" x2="700" y2="280" stroke="#333" stroke-width="2"/>
                                <line x1="90" y1="210" x2="90" y2="280" stroke="#333" stroke-width="1"/>
                                <line x1="230" y1="220" x2="230" y2="280" stroke="#333" stroke-width="1"/>
                                <line x1="390" y1="240" x2="390" y2="280" stroke="#333" stroke-width="1"/>
                                <line x1="550" y1="220" x2="550" y2="280" stroke="#333" stroke-width="1"/>
                                <line x1="690" y1="210" x2="690" y2="280" stroke="#333" stroke-width="1"/>

                                <!-- Ground symbols -->
                                <polygon points="85,280 95,280 90,290" fill="#333"/>
                                <polygon points="225,280 235,280 230,290" fill="#333"/>
                                <polygon points="385,280 395,280 390,290" fill="#333"/>
                                <polygon points="545,280 555,280 550,290" fill="#333"/>
                                <polygon points="685,280 695,280 690,290" fill="#333"/>
                            </g>

                            <!-- Component Labels -->
                            <text x="90" y="130" text-anchor="middle" font-size="10" fill="#666">Electrodes</text>
                            <text x="230" y="130" text-anchor="middle" font-size="10" fill="#666">Instrumentation Amp</text>
                            <text x="390" y="110" text-anchor="middle" font-size="10" fill="#666">Signal Conditioning</text>
                            <text x="550" y="130" text-anchor="middle" font-size="10" fill="#666">Level Shifter</text>
                            <text x="690" y="130" text-anchor="middle" font-size="10" fill="#666">Microcontroller</text>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Component Calculator -->
            <div class="interactive-circuit">
                <h2>Component Value Calculator</h2>
                <div class="circuit-controls">
                    <div class="control-group">
                        <label for="calculatorType">Calculator Type</label>
                        <select id="calculatorType" onchange="switchCalculator()">
                            <option value="gain">Gain Calculation</option>
                            <option value="filter">Filter Design</option>
                            <option value="power">Power Analysis</option>
                            <option value="noise">Noise Analysis</option>
                        </select>
                    </div>
                </div>
                <div id="calculatorContent">
                    <!-- Calculator content will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/error-handler.js"></script>
    <script src="../js/navigation-utils.js"></script>
    <script src="../js/main.js"></script>
    <script src="../data/ecg-data.js"></script>
    <script src="../js/circuit-design.js"></script>
    <script src="../js/circuit-calculators.js"></script>
</body>
</html>
