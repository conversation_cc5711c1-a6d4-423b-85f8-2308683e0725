<!DOCTYPE html>
<!--
    Circuit Design - ECG Signal System
    Author: Dr. <PERSON>smail
    Institution: SUST-BME (Sudan University of Science and Technology - Biomedical Engineering)
    Year: 2025
    Copyright: <EMAIL>
    Contact: +249912867327, +966538076790

    ECG Circuit Design and Analysis Platform
    Features: Component Calculators, Circuit Analysis, Design Tools
-->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="author" content="Dr. <PERSON>smail">
    <meta name="description" content="ECG Circuit Design Platform - SUST-BME 2025">
    <title>Circuit Design - ECG Signal System | Dr. <PERSON></title>
    <link rel="stylesheet" href="../css/styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        .circuit-container {
            padding: 100px 0 50px;
            background: #f8f9fa;
        }

        .circuit-stage {
            background: white;
            margin: 2rem 0;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stage-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .stage-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-right: 1rem;
        }

        .component-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .component-card {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .component-card h4 {
            color: #333;
            margin-bottom: 0.5rem;
        }

        .spec-list {
            list-style: none;
            padding: 0;
        }

        .spec-list li {
            padding: 0.25rem 0;
            color: #666;
            font-size: 0.9rem;
        }

        .interactive-circuit {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .circuit-controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .control-group label {
            font-weight: 600;
            color: #333;
        }

        .control-group input,
        .control-group select {
            padding: 0.5rem;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }

        .control-group input:focus,
        .control-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .frequency-response {
            height: 400px;
            margin: 1rem 0;
        }

        .schematic-viewer {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }

        .schematic-placeholder {
            font-size: 4rem;
            color: #ddd;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-heartbeat"></i>
                <span>ECG Simulator</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="../index.html" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="#circuit" class="nav-link active">Circuit Design</a>
                </li>
                <li class="nav-item">
                    <a href="signal-analysis.html" class="nav-link">Signal Analysis</a>
                </li>
                <li class="nav-item">
                    <a href="pcb-viewer.html" class="nav-link">PCB Viewer</a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Circuit Design Content -->
    <div class="circuit-container">
        <div class="container">
            <h1 class="section-title">ECG Circuit Design</h1>

            <!-- Interactive Circuit Simulator -->
            <div class="interactive-circuit">
                <h2>Interactive Circuit Simulator</h2>
                <div class="circuit-controls">
                    <div class="control-group">
                        <label for="inputSignal">Input Signal</label>
                        <select id="inputSignal">
                            <option value="ecg">Normal ECG</option>
                            <option value="sine">Sine Wave</option>
                            <option value="square">Square Wave</option>
                            <option value="noise">Noise</option>
                        </select>
                    </div>
                    <div class="control-group">
                        <label for="gain">System Gain</label>
                        <input type="range" id="gain" min="100" max="2000" value="1100">
                        <span id="gainValue">1100x</span>
                    </div>
                    <div class="control-group">
                        <label for="frequency">Frequency (Hz)</label>
                        <input type="range" id="frequency" min="0.1" max="100" value="1" step="0.1">
                        <span id="frequencyValue">1.0 Hz</span>
                    </div>
                    <div class="control-group">
                        <button type="button" class="btn btn-primary" onclick="updateSimulation()">
                            <i class="fas fa-play"></i> Run Simulation
                        </button>
                    </div>
                </div>

                <div class="frequency-response" id="frequencyResponse"></div>
            </div>

            <!-- Stage 1: Input Protection and Buffering -->
            <div class="circuit-stage">
                <div class="stage-header">
                    <div class="stage-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div>
                        <h2>Stage 1: Input Protection & Buffering</h2>
                        <p>Electrode interface and input protection circuitry</p>
                    </div>
                </div>

                <div class="component-grid">
                    <div class="component-card">
                        <h4>Input Protection</h4>
                        <ul class="spec-list">
                            <li>ESD Protection Diodes</li>
                            <li>Input Impedance: >10MΩ</li>
                            <li>Common Mode Range: ±10V</li>
                            <li>Overvoltage Protection</li>
                        </ul>
                    </div>
                    <div class="component-card">
                        <h4>Electrode Interface</h4>
                        <ul class="spec-list">
                            <li>Ag/AgCl Electrodes</li>
                            <li>Contact Impedance: <2kΩ</li>
                            <li>Offset Voltage: <100μV</li>
                            <li>3-Lead Configuration</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Stage 2: Instrumentation Amplifier -->
            <div class="circuit-stage">
                <div class="stage-header">
                    <div class="stage-icon">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </div>
                    <div>
                        <h2>Stage 2: Instrumentation Amplifier (INA128UA)</h2>
                        <p>High-precision differential amplification with excellent CMRR</p>
                    </div>
                </div>

                <div class="component-grid">
                    <div class="component-card">
                        <h4>INA128UA Specifications</h4>
                        <ul class="spec-list">
                            <li>Gain: 1 to 10,000 (Programmable)</li>
                            <li>CMRR: 120dB min</li>
                            <li>Input Offset: 50μV max</li>
                            <li>Input Bias Current: 5nA max</li>
                            <li>Bandwidth: 200kHz</li>
                        </ul>
                    </div>
                    <div class="component-card">
                        <h4>Gain Setting</h4>
                        <ul class="spec-list">
                            <li>Gain = 1 + (50kΩ/RG)</li>
                            <li>RG = 47Ω (for gain ≈ 1000)</li>
                            <li>Precision Metal Film Resistor</li>
                            <li>Temperature Coefficient: 25ppm/°C</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Stage 3: Filtering -->
            <div class="circuit-stage">
                <div class="stage-header">
                    <div class="stage-icon">
                        <i class="fas fa-filter"></i>
                    </div>
                    <div>
                        <h2>Stage 3: Analog Filtering</h2>
                        <p>Multi-stage filtering for noise reduction and signal conditioning</p>
                    </div>
                </div>

                <div class="component-grid">
                    <div class="component-card">
                        <h4>High-Pass Filter</h4>
                        <ul class="spec-list">
                            <li>Cutoff: 0.5Hz (-3dB)</li>
                            <li>Type: 2nd Order Butterworth</li>
                            <li>Purpose: DC offset removal</li>
                            <li>Implementation: Sallen-Key</li>
                        </ul>
                    </div>
                    <div class="component-card">
                        <h4>Low-Pass Filter</h4>
                        <ul class="spec-list">
                            <li>Cutoff: 150Hz (-3dB)</li>
                            <li>Type: 4th Order Butterworth</li>
                            <li>Purpose: Anti-aliasing</li>
                            <li>Op-Amp: OPA2131UA</li>
                        </ul>
                    </div>
                    <div class="component-card">
                        <h4>Notch Filter</h4>
                        <ul class="spec-list">
                            <li>Frequency: 50/60Hz</li>
                            <li>Q Factor: 30</li>
                            <li>Rejection: >40dB</li>
                            <li>Purpose: Power line interference</li>
                        </ul>
                    </div>
                    <div class="component-card">
                        <h4>Buffer Amplifier</h4>
                        <ul class="spec-list">
                            <li>Op-Amp: LM358</li>
                            <li>Gain: Unity (1x)</li>
                            <li>Purpose: Impedance matching</li>
                            <li>Output Drive: 10mA</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Stage 4: Output Stage -->
            <div class="circuit-stage">
                <div class="stage-header">
                    <div class="stage-icon">
                        <i class="fas fa-microchip"></i>
                    </div>
                    <div>
                        <h2>Stage 4: Output Stage & ADC Interface</h2>
                        <p>Level shifting and ADC interface for STM32F4</p>
                    </div>
                </div>

                <div class="component-grid">
                    <div class="component-card">
                        <h4>Level Shifter (OP07D)</h4>
                        <ul class="spec-list">
                            <li>Input Range: ±5.5V (after amplification)</li>
                            <li>Output Range: 0-3.3V</li>
                            <li>DC Offset: 1.65V (VCC/2)</li>
                            <li>Gain: Unity (1x)</li>
                            <li>Input Offset: <25μV</li>
                            <li>Bandwidth: 600kHz</li>
                        </ul>
                    </div>
                    <div class="component-card">
                        <h4>STM32F4 ADC Interface</h4>
                        <ul class="spec-list">
                            <li>Resolution: 12-bit (4096 levels)</li>
                            <li>Sample Rate: 256Hz (ECG)</li>
                            <li>Reference: 3.3V (VREF+)</li>
                            <li>Input Impedance: >1MΩ</li>
                            <li>Conversion Time: 1.17μs</li>
                            <li>DNL: ±1 LSB, INL: ±2 LSB</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Design Calculations -->
            <div class="circuit-stage">
                <div class="stage-header">
                    <div class="stage-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div>
                        <h2>Design Calculations & Analysis</h2>
                        <p>Key design equations and performance calculations</p>
                    </div>
                </div>

                <div class="component-grid">
                    <div class="component-card">
                        <h4>Gain Calculations</h4>
                        <ul class="spec-list">
                            <li><strong>INA128UA Gain:</strong> G = 1 + (50kΩ/RG)</li>
                            <li><strong>RG Value:</strong> 47Ω (for G ≈ 1064)</li>
                            <li><strong>Total System Gain:</strong> 1100x (60.8dB)</li>
                            <li><strong>Input Signal:</strong> 1mV ECG → 1.1V output</li>
                            <li><strong>Gain Accuracy:</strong> ±0.5% (1% resistor tolerance)</li>
                        </ul>
                    </div>
                    <div class="component-card">
                        <h4>Filter Calculations</h4>
                        <ul class="spec-list">
                            <li><strong>HPF Cutoff:</strong> fc = 1/(2π√(R×C)) = 0.5Hz</li>
                            <li><strong>LPF Cutoff:</strong> fc = 1/(2πRC) = 150Hz</li>
                            <li><strong>Notch Frequency:</strong> f0 = 1/(2πRC) = 50Hz</li>
                            <li><strong>Q Factor:</strong> Q = f0/BW = 30</li>
                            <li><strong>Phase Margin:</strong> >60° (stability)</li>
                        </ul>
                    </div>
                    <div class="component-card">
                        <h4>Noise Analysis</h4>
                        <ul class="spec-list">
                            <li><strong>INA128UA Noise:</strong> 8nV/√Hz @ 1kHz</li>
                            <li><strong>Thermal Noise (1kΩ):</strong> 4nV/√Hz</li>
                            <li><strong>Total Input Noise:</strong> <2μVrms (0.5-150Hz)</li>
                            <li><strong>SNR:</strong> 20×log(1mV/2μV) = 54dB</li>
                            <li><strong>Dynamic Range:</strong> 72dB (12-bit ADC)</li>
                        </ul>
                    </div>
                    <div class="component-card">
                        <h4>Power Budget</h4>
                        <ul class="spec-list">
                            <li><strong>INA128UA:</strong> 700μA × 18V = 12.6mW</li>
                            <li><strong>OPA2131UA:</strong> 900μA × 18V = 16.2mW</li>
                            <li><strong>LM358:</strong> 500μA × 18V = 9.0mW</li>
                            <li><strong>OP07D:</strong> 300μA × 18V = 5.4mW</li>
                            <li><strong>Total Power:</strong> 43.2mW</li>
                            <li><strong>Battery Life:</strong> 208 hours (9V, 500mAh)</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Performance Specifications -->
            <div class="circuit-stage">
                <div class="stage-header">
                    <div class="stage-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div>
                        <h2>Performance Specifications</h2>
                        <p>Measured and calculated system performance metrics</p>
                    </div>
                </div>

                <div class="component-grid">
                    <div class="component-card">
                        <h4>Frequency Response</h4>
                        <ul class="spec-list">
                            <li><strong>Passband:</strong> 0.5Hz - 150Hz (-3dB)</li>
                            <li><strong>Gain Flatness:</strong> ±0.5dB in passband</li>
                            <li><strong>High-freq Rolloff:</strong> -80dB/decade (>150Hz)</li>
                            <li><strong>Low-freq Rolloff:</strong> -40dB/decade (<0.5Hz)</li>
                            <li><strong>Group Delay:</strong> <10ms (linear phase)</li>
                        </ul>
                    </div>
                    <div class="component-card">
                        <h4>Distortion & Linearity</h4>
                        <ul class="spec-list">
                            <li><strong>THD:</strong> <0.1% @ 1mV input, 10Hz</li>
                            <li><strong>IMD:</strong> <0.05% (19kHz + 20kHz test)</li>
                            <li><strong>Linearity Error:</strong> <0.1% FSR</li>
                            <li><strong>Saturation Level:</strong> ±4.5V output</li>
                            <li><strong>Slew Rate Limit:</strong> 20V/μs (OPA2131UA)</li>
                        </ul>
                    </div>
                    <div class="component-card">
                        <h4>Common Mode Performance</h4>
                        <ul class="spec-list">
                            <li><strong>CMRR:</strong> >120dB @ DC, >80dB @ 50Hz</li>
                            <li><strong>CM Input Range:</strong> ±10V</li>
                            <li><strong>CM Rejection Ratio:</strong> 1,000,000:1</li>
                            <li><strong>Power Supply Rejection:</strong> >80dB</li>
                            <li><strong>Temperature Drift:</strong> <5μV/°C</li>
                        </ul>
                    </div>
                    <div class="component-card">
                        <h4>Digital Interface</h4>
                        <ul class="spec-list">
                            <li><strong>ADC Resolution:</strong> 12-bit (0.8mV/LSB)</li>
                            <li><strong>Effective Bits:</strong> 10.5 ENOB</li>
                            <li><strong>Sampling Jitter:</strong> <1ns RMS</li>
                            <li><strong>Aperture Delay:</strong> 7ns</li>
                            <li><strong>Digital Filter:</strong> 50Hz notch (software)</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Schematic Viewer -->
            <div class="interactive-circuit">
                <h2>Circuit Schematic</h2>
                <div class="schematic-viewer">
                    <div class="schematic-placeholder">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <p>Interactive schematic viewer would be displayed here</p>
                    <p>Click on components to view detailed specifications</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/main.js"></script>
    <script src="../data/ecg-data.js"></script>
    <script>
        // Circuit simulation functions
        function updateSimulation() {
            const inputSignal = document.getElementById('inputSignal').value;
            const gain = document.getElementById('gain').value;
            const frequency = document.getElementById('frequency').value;

            // Update display values
            document.getElementById('gainValue').textContent = gain + 'x';
            document.getElementById('frequencyValue').textContent = frequency + ' Hz';

            // Generate frequency response
            generateFrequencyResponse();
        }

        function generateFrequencyResponse() {
            const frequencies = [];
            const magnitude = [];
            const phase = [];

            // Generate frequency points from 0.1 Hz to 1000 Hz
            for (let i = 0.1; i <= 1000; i *= 1.1) {
                frequencies.push(i);

                // Simplified frequency response model
                let mag = 60; // 60dB base gain (1000x)

                // High-pass response (0.5Hz cutoff)
                if (i < 0.5) {
                    mag += 20 * Math.log10(i / 0.5);
                }

                // Low-pass response (150Hz cutoff)
                if (i > 150) {
                    mag -= 40 * Math.log10(i / 150); // 4th order (-80dB/decade)
                }

                // Notch filter (50Hz)
                if (i > 45 && i < 55) {
                    mag -= 40; // 40dB notch
                }

                magnitude.push(mag);

                // Phase response (simplified)
                let ph = 0;
                if (i < 0.5) ph += 90;
                if (i > 150) ph -= 180 * Math.log10(i / 150);
                phase.push(ph);
            }

            // Plot frequency response
            const trace1 = {
                x: frequencies,
                y: magnitude,
                type: 'scatter',
                mode: 'lines',
                name: 'Magnitude',
                line: { color: '#667eea', width: 3 }
            };

            const layout = {
                title: 'ECG Amplifier Frequency Response',
                xaxis: {
                    title: 'Frequency (Hz)',
                    type: 'log',
                    range: [-1, 3]
                },
                yaxis: {
                    title: 'Magnitude (dB)',
                    range: [-20, 80]
                },
                grid: true,
                margin: { t: 50, r: 50, b: 50, l: 60 }
            };

            Plotly.newPlot('frequencyResponse', [trace1], layout, {responsive: true});
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            generateFrequencyResponse();

            // Add event listeners for real-time updates
            document.getElementById('gain').addEventListener('input', updateSimulation);
            document.getElementById('frequency').addEventListener('input', updateSimulation);
        });
    </script>
</body>
</html>
