<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Analysis - ECG Signal System</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/component-pages.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-heartbeat"></i>
                <span>ECG Simulator</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="../index.html" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="circuit-design.html" class="nav-link">Circuit Design</a>
                </li>
                <li class="nav-item">
                    <a href="signal-analysis.html" class="nav-link">Signal Analysis</a>
                </li>
                <li class="nav-item">
                    <a href="virtual-workbench.html" class="nav-link">Virtual Workbench</a>
                </li>
                <li class="nav-item">
                    <a href="interactive-diagrams.html" class="nav-link">Interactive Diagrams</a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Breadcrumb Navigation -->
    <div class="breadcrumb-nav">
        <div class="container">
            <nav class="breadcrumb">
                <a href="../index.html" class="breadcrumb-link">
                    <i class="fas fa-home"></i> Home
                </a>
                <span class="breadcrumb-separator">/</span>
                <span class="breadcrumb-current">Data Analysis</span>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <div class="component-page">
        <!-- Hero Section -->
        <section class="component-hero">
            <div class="container">
                <div class="hero-content">
                    <div class="hero-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h1>Data Analysis</h1>
                    <p class="hero-subtitle">Advanced ECG Signal Analysis and Visualization Tools</p>
                    <div class="hero-stats">
                        <div class="stat-item">
                            <span class="stat-value">256 Hz</span>
                            <span class="stat-label">Sampling Rate</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">12-bit</span>
                            <span class="stat-label">Resolution</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-value">0.5-150 Hz</span>
                            <span class="stat-label">Bandwidth</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Analysis Tools Section -->
        <section class="component-overview">
            <div class="container">
                <h2>Analysis Capabilities</h2>
                <div class="overview-grid">
                    <div class="overview-content">
                        <h3>Time Domain Analysis</h3>
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i> Heart Rate Detection</li>
                            <li><i class="fas fa-check"></i> R-R Interval Analysis</li>
                            <li><i class="fas fa-check"></i> QRS Complex Detection</li>
                            <li><i class="fas fa-check"></i> P-Wave and T-Wave Analysis</li>
                            <li><i class="fas fa-check"></i> Arrhythmia Detection</li>
                        </ul>

                        <h3>Frequency Domain Analysis</h3>
                        <ul class="feature-list">
                            <li><i class="fas fa-check"></i> Power Spectral Density</li>
                            <li><i class="fas fa-check"></i> Heart Rate Variability (HRV)</li>
                            <li><i class="fas fa-check"></i> Noise Analysis</li>
                            <li><i class="fas fa-check"></i> Filter Response Verification</li>
                        </ul>
                    </div>
                    
                    <div class="overview-visual">
                        <div class="analysis-preview">
                            <h3>Real-time Analysis</h3>
                            <div id="analysisPlot" class="plot-container">
                                <!-- Analysis plot will be rendered here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Interactive Analysis Section -->
        <section class="signal-quality">
            <div class="container">
                <h2>Interactive Analysis Tools</h2>
                <div class="analysis-tools-grid">
                    <div class="tool-card">
                        <div class="tool-header">
                            <i class="fas fa-heartbeat"></i>
                            <h3>Heart Rate Monitor</h3>
                        </div>
                        <div class="tool-content">
                            <div class="metric-display">
                                <span class="metric-value" id="heartRate">72</span>
                                <span class="metric-unit">BPM</span>
                            </div>
                            <div class="metric-status">
                                <span class="status-indicator normal"></span>
                                <span>Normal Range</span>
                            </div>
                        </div>
                    </div>

                    <div class="tool-card">
                        <div class="tool-header">
                            <i class="fas fa-wave-square"></i>
                            <h3>Signal Quality</h3>
                        </div>
                        <div class="tool-content">
                            <div class="metric-display">
                                <span class="metric-value" id="signalQuality">94</span>
                                <span class="metric-unit">%</span>
                            </div>
                            <div class="metric-status">
                                <span class="status-indicator good"></span>
                                <span>Excellent Quality</span>
                            </div>
                        </div>
                    </div>

                    <div class="tool-card">
                        <div class="tool-header">
                            <i class="fas fa-chart-bar"></i>
                            <h3>SNR Analysis</h3>
                        </div>
                        <div class="tool-content">
                            <div class="metric-display">
                                <span class="metric-value" id="snrValue">62</span>
                                <span class="metric-unit">dB</span>
                            </div>
                            <div class="metric-status">
                                <span class="status-indicator good"></span>
                                <span>High SNR</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Analysis Results Section -->
        <section class="component-specs">
            <div class="container">
                <h2>Analysis Results</h2>
                <div class="specs-grid">
                    <div class="spec-card">
                        <h3><i class="fas fa-clock"></i> Temporal Metrics</h3>
                        <table class="specs-table">
                            <tr><td>Average Heart Rate:</td><td>72 BPM</td></tr>
                            <tr><td>R-R Interval:</td><td>833 ms</td></tr>
                            <tr><td>QRS Duration:</td><td>95 ms</td></tr>
                            <tr><td>P-R Interval:</td><td>165 ms</td></tr>
                            <tr><td>Q-T Interval:</td><td>385 ms</td></tr>
                            <tr><td>HRV (RMSSD):</td><td>42 ms</td></tr>
                        </table>
                    </div>
                    
                    <div class="spec-card">
                        <h3><i class="fas fa-chart-area"></i> Frequency Metrics</h3>
                        <table class="specs-table">
                            <tr><td>Dominant Frequency:</td><td>1.2 Hz</td></tr>
                            <tr><td>Power (0.5-40Hz):</td><td>-12 dBm</td></tr>
                            <tr><td>50Hz Rejection:</td><td>-45 dB</td></tr>
                            <tr><td>Baseline Wander:</td><td>&lt;0.1 Hz</td></tr>
                            <tr><td>High Freq Noise:</td><td>&lt;-40 dB</td></tr>
                            <tr><td>THD:</td><td>0.8%</td></tr>
                        </table>
                    </div>
                    
                    <div class="spec-card">
                        <h3><i class="fas fa-stethoscope"></i> Clinical Assessment</h3>
                        <table class="specs-table">
                            <tr><td>Rhythm:</td><td>Sinus Rhythm</td></tr>
                            <tr><td>Rate Classification:</td><td>Normal</td></tr>
                            <tr><td>QRS Morphology:</td><td>Normal</td></tr>
                            <tr><td>ST Segment:</td><td>Isoelectric</td></tr>
                            <tr><td>T Wave:</td><td>Normal</td></tr>
                            <tr><td>Overall Assessment:</td><td>Normal ECG</td></tr>
                        </table>
                    </div>
                </div>
            </div>
        </section>

        <!-- Navigation Links -->
        <section class="component-navigation">
            <div class="container">
                <div class="nav-links">
                    <a href="../index.html" class="nav-link-btn">
                        <i class="fas fa-home"></i> Back to Home
                    </a>
                    <a href="signal-analysis.html" class="nav-link-btn next">
                        Signal Analysis Tools <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </section>
    </div>

    <!-- Scripts -->
    <script src="../js/error-handler.js"></script>
    <script src="../js/main.js"></script>
    <script src="../js/component-pages.js"></script>
    <script src="../data/ecg-data.js"></script>
    <script src="../js/console-test.js"></script>

    <style>
        .analysis-tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .tool-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            text-align: center;
        }

        .tool-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
        }

        .tool-header i {
            font-size: 1.5rem;
        }

        .tool-content {
            padding: 2rem;
        }

        .metric-display {
            margin-bottom: 1rem;
        }

        .metric-value {
            font-size: 3rem;
            font-weight: bold;
            color: #333;
        }

        .metric-unit {
            font-size: 1.2rem;
            color: #666;
            margin-left: 0.5rem;
        }

        .metric-status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            color: #666;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .status-indicator.normal { background: #4CAF50; }
        .status-indicator.good { background: #2196F3; }
        .status-indicator.warning { background: #FF9800; }
        .status-indicator.error { background: #f44336; }

        .analysis-preview {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .analysis-preview h3 {
            text-align: center;
            color: #333;
            margin-bottom: 1.5rem;
        }
    </style>
</body>
</html>
