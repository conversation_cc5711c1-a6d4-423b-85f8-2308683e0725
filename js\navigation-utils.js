// Navigation Utilities for ECG Signal System
// Provides comprehensive navigation features including history tracking, return buttons, and quick navigation

class NavigationManager {
    constructor() {
        this.history = [];
        this.currentPage = null;
        this.maxHistoryLength = 10;
        this.isInitialized = false;

        // Page definitions with metadata (only existing pages)
        this.pages = {
            'index.html': { title: 'Home', icon: 'fas fa-home', category: 'main' },
            'pages/circuit-design.html': { title: 'Circuit Design', icon: 'fas fa-microchip', category: 'design' },
            'pages/signal-analysis.html': { title: 'Signal Analysis', icon: 'fas fa-wave-square', category: 'analysis' },
            'pages/pcb-viewer.html': { title: 'PCB Viewer', icon: 'fas fa-layer-group', category: 'design' },
            'pages/virtual-workbench.html': { title: 'Virtual Workbench', icon: 'fas fa-tools', category: 'simulation' },
            'pages/interactive-diagrams.html': { title: 'Interactive Diagrams', icon: 'fas fa-sitemap', category: 'documentation' },
            'pages/data-analysis.html': { title: 'Data Analysis', icon: 'fas fa-chart-line', category: 'analysis' },
            'pages/ecg-electrodes.html': { title: 'ECG Electrodes', icon: 'fas fa-user', category: 'components' },
            'pages/navigation-demo.html': { title: 'Navigation Demo', icon: 'fas fa-compass', category: 'demo' }
        };

        // Fallback pages for missing references
        this.fallbackPages = {
            'amplifier': 'pages/circuit-design.html',
            'filters': 'pages/circuit-design.html',
            'adc': 'pages/circuit-design.html',
            'processing': 'pages/data-analysis.html'
        };

        this.init();
    }

    init() {
        if (this.isInitialized) return;

        this.loadHistory();
        this.detectCurrentPage();
        this.addCurrentPageToHistory();
        this.createNavigationElements();
        this.setupEventListeners();

        this.isInitialized = true;
        console.log('✅ Navigation Manager initialized');
    }

    detectCurrentPage() {
        const path = window.location.pathname;
        const filename = path.split('/').pop() || 'index.html';

        // Handle different path scenarios
        if (filename === '' || filename === '/') {
            this.currentPage = 'index.html';
        } else if (filename.includes('.html')) {
            this.currentPage = this.getRelativePath(path);
        } else {
            this.currentPage = 'index.html';
        }

        console.log('Current page detected:', this.currentPage);
    }

    getRelativePath(fullPath) {
        // Convert absolute path to relative path for consistency
        if (fullPath.includes('pages/')) {
            const pagesPart = fullPath.substring(fullPath.indexOf('pages/'));
            return pagesPart;
        } else if (fullPath.endsWith('index.html') || fullPath.endsWith('/')) {
            return 'index.html';
        }
        return fullPath.split('/').pop();
    }

    addCurrentPageToHistory() {
        if (!this.currentPage) return;

        const pageInfo = this.pages[this.currentPage];
        if (!pageInfo) return;

        const historyItem = {
            path: this.currentPage,
            title: pageInfo.title,
            icon: pageInfo.icon,
            timestamp: Date.now(),
            url: this.getCurrentURL()
        };

        // Remove if already exists to avoid duplicates
        this.history = this.history.filter(item => item.path !== this.currentPage);

        // Add to beginning of history
        this.history.unshift(historyItem);

        // Limit history length
        if (this.history.length > this.maxHistoryLength) {
            this.history = this.history.slice(0, this.maxHistoryLength);
        }

        this.saveHistory();
    }

    getCurrentURL() {
        return window.location.href;
    }

    createNavigationElements() {
        this.createReturnButton();
        this.createHistoryPanel();
        this.createQuickNavMenu();
        this.enhanceBreadcrumbs();
    }

    createReturnButton() {
        // Remove existing return button if any
        const existing = document.querySelector('.return-nav-button');
        if (existing) existing.remove();

        const returnButton = document.createElement('a');
        returnButton.className = 'return-nav-button';
        returnButton.innerHTML = '<i class="fas fa-arrow-left"></i> Back';
        returnButton.title = 'Go back to previous page';

        returnButton.addEventListener('click', (e) => {
            e.preventDefault();
            this.goBack();
        });

        // Show history panel on right click
        returnButton.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.toggleHistoryPanel();
        });

        document.body.appendChild(returnButton);
    }

    createHistoryPanel() {
        // Remove existing panel if any
        const existing = document.querySelector('.nav-history-panel');
        if (existing) existing.remove();

        const panel = document.createElement('div');
        panel.className = 'nav-history-panel';
        panel.innerHTML = `
            <div class="nav-history-header">
                <h3><i class="fas fa-history"></i> Navigation History</h3>
                <button class="nav-history-close" title="Close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="nav-history-list" id="nav-history-list">
                <!-- History items will be populated here -->
            </div>
        `;

        // Setup close button
        panel.querySelector('.nav-history-close').addEventListener('click', () => {
            this.hideHistoryPanel();
        });

        document.body.appendChild(panel);
        this.updateHistoryPanel();
    }

    createQuickNavMenu() {
        // Remove existing menu if any
        const existing = document.querySelector('.quick-nav-menu');
        if (existing) existing.remove();

        const quickNav = document.createElement('div');
        quickNav.className = 'quick-nav-menu';

        // Generate quick nav items dynamically based on current page location
        const homeLink = this.getHomePath();
        const isInPages = this.currentPage && this.currentPage.startsWith('pages/');

        quickNav.innerHTML = `
            <button type="button" class="quick-nav-toggle" title="Quick Navigation">
                <i class="fas fa-compass"></i>
            </button>
            <div class="quick-nav-items">
                <a href="${homeLink}" class="quick-nav-item" title="Home">
                    <i class="fas fa-home"></i>
                </a>
                <a href="${isInPages ? '' : 'pages/'}circuit-design.html" class="quick-nav-item" title="Circuit Design">
                    <i class="fas fa-microchip"></i>
                </a>
                <a href="${isInPages ? '' : 'pages/'}signal-analysis.html" class="quick-nav-item" title="Signal Analysis">
                    <i class="fas fa-wave-square"></i>
                </a>
                <a href="${isInPages ? '' : 'pages/'}virtual-workbench.html" class="quick-nav-item" title="Virtual Workbench">
                    <i class="fas fa-tools"></i>
                </a>
                <a href="${isInPages ? '' : 'pages/'}interactive-diagrams.html" class="quick-nav-item" title="Interactive Diagrams">
                    <i class="fas fa-sitemap"></i>
                </a>
                <a href="${isInPages ? '' : 'pages/'}navigation-demo.html" class="quick-nav-item" title="Navigation Demo">
                    <i class="fas fa-compass"></i>
                </a>
            </div>
        `;

        // Setup toggle functionality
        const toggle = quickNav.querySelector('.quick-nav-toggle');
        toggle.addEventListener('click', () => {
            quickNav.classList.toggle('active');
        });

        // Close menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!quickNav.contains(e.target)) {
                quickNav.classList.remove('active');
            }
        });

        document.body.appendChild(quickNav);
    }

    enhanceBreadcrumbs() {
        const breadcrumbNav = document.querySelector('.breadcrumb-nav');
        if (!breadcrumbNav) return;

        // Add enhanced class
        breadcrumbNav.classList.add('enhanced-breadcrumb');

        // Create container if it doesn't exist
        let container = breadcrumbNav.querySelector('.breadcrumb-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'breadcrumb-container';

            // Move existing breadcrumb content
            const existingContent = breadcrumbNav.innerHTML;
            container.innerHTML = existingContent;
            breadcrumbNav.innerHTML = '';
            breadcrumbNav.appendChild(container);
        }

        // Add navigation actions
        const actions = document.createElement('div');
        actions.className = 'breadcrumb-nav-actions';
        actions.innerHTML = `
            <a href="javascript:void(0)" class="breadcrumb-action-btn" onclick="navigationManager.goBack()">
                <i class="fas fa-arrow-left"></i> Back
            </a>
            <a href="../index.html" class="breadcrumb-action-btn">
                <i class="fas fa-home"></i> Home
            </a>
        `;

        container.appendChild(actions);
    }

    setupEventListeners() {
        // Handle browser back/forward buttons
        window.addEventListener('popstate', () => {
            this.detectCurrentPage();
            this.addCurrentPageToHistory();
        });

        // Handle page unload to save history
        window.addEventListener('beforeunload', () => {
            this.saveHistory();
        });
    }

    goBack() {
        try {
            if (this.history.length > 1) {
                // Get the previous page (skip current page at index 0)
                const previousPage = this.history[1];
                if (previousPage && this.isValidPage(previousPage.path)) {
                    window.location.href = this.resolveURL(previousPage.path);
                    return;
                }
            }

            // Try browser back first
            if (window.history.length > 1) {
                window.history.back();
                return;
            }

            // Fallback to home page
            window.location.href = this.getHomePath();
        } catch (error) {
            console.error('Error in goBack:', error);
            // Ultimate fallback
            window.location.href = this.getHomePath();
        }
    }

    isValidPage(path) {
        return this.pages.hasOwnProperty(path);
    }

    getHomePath() {
        // Determine correct path to home based on current location
        if (this.currentPage && this.currentPage.startsWith('pages/')) {
            return '../index.html';
        }
        return 'index.html';
    }

    resolveURL(path) {
        // Resolve relative URLs based on current page location
        if (this.currentPage && this.currentPage.startsWith('pages/')) {
            if (path === 'index.html') {
                return '../index.html';
            } else if (!path.startsWith('pages/') && !path.startsWith('../')) {
                return path;
            }
        }
        return path;
    }

    toggleHistoryPanel() {
        const panel = document.querySelector('.nav-history-panel');
        if (panel) {
            panel.classList.toggle('active');
        }
    }

    hideHistoryPanel() {
        const panel = document.querySelector('.nav-history-panel');
        if (panel) {
            panel.classList.remove('active');
        }
    }

    updateHistoryPanel() {
        const listContainer = document.getElementById('nav-history-list');
        if (!listContainer) return;

        listContainer.innerHTML = '';

        if (this.history.length === 0) {
            listContainer.innerHTML = '<p style="text-align: center; color: #666; padding: 1rem;">No navigation history</p>';
            return;
        }

        this.history.forEach((item, index) => {
            if (index === 0) return; // Skip current page

            const historyItem = document.createElement('a');
            historyItem.className = 'nav-history-item';
            historyItem.href = this.resolveURL(item.path);
            historyItem.innerHTML = `
                <i class="${item.icon}"></i>
                <div class="nav-history-item-content">
                    <div class="nav-history-item-title">${item.title}</div>
                    <div class="nav-history-item-path">${item.path}</div>
                </div>
            `;

            listContainer.appendChild(historyItem);
        });
    }

    saveHistory() {
        try {
            localStorage.setItem('ecg-nav-history', JSON.stringify(this.history));
        } catch (e) {
            console.warn('Could not save navigation history:', e);
        }
    }

    loadHistory() {
        try {
            const saved = localStorage.getItem('ecg-nav-history');
            if (saved) {
                this.history = JSON.parse(saved);
            }
        } catch (e) {
            console.warn('Could not load navigation history:', e);
            this.history = [];
        }
    }

    // Public API methods
    navigateToPage(pagePath) {
        window.location.href = this.resolveURL(pagePath);
    }

    clearHistory() {
        this.history = [];
        this.saveHistory();
        this.updateHistoryPanel();
    }

    getPageInfo(path) {
        return this.pages[path] || null;
    }
}

// Initialize navigation manager when DOM is ready
let navigationManager;

document.addEventListener('DOMContentLoaded', function() {
    navigationManager = new NavigationManager();

    // Make it globally available
    window.navigationManager = navigationManager;
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NavigationManager;
}
