/* Component Pages Styles */

.component-page {
    padding-top: 70px;
    min-height: 100vh;
}

/* Breadcrumb Navigation */
.breadcrumb-nav {
    background: white;
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.breadcrumb-link {
    color: #667eea;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    transition: color 0.3s ease;
}

.breadcrumb-link:hover {
    color: #764ba2;
}

.breadcrumb-separator {
    color: #999;
}

.breadcrumb-current {
    color: #333;
    font-weight: 600;
}

/* Component Hero Section */
.component-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.hero-icon {
    width: 100px;
    height: 100px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    font-size: 3rem;
}

.component-hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Component Overview */
.component-overview {
    padding: 4rem 0;
    background: white;
}

.overview-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.overview-content h2 {
    color: #333;
    font-size: 2.2rem;
    margin-bottom: 1.5rem;
}

.overview-content h3 {
    color: #667eea;
    font-size: 1.5rem;
    margin: 2rem 0 1rem 0;
}

.overview-content p {
    color: #666;
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.feature-list {
    list-style: none;
    padding: 0;
}

.feature-list li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    color: #666;
}

.feature-list i {
    color: #4CAF50;
    font-size: 0.9rem;
}

.placement-info {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.placement-item {
    margin-bottom: 0.75rem;
    color: #666;
}

.placement-item:last-child {
    margin-bottom: 0;
}

/* Overview Visual */
.overview-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.electrode-diagram {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
    width: 100%;
}

.electrode-diagram h3 {
    color: #333;
    margin-bottom: 2rem;
}

.electrode-layout {
    position: relative;
    width: 200px;
    height: 300px;
    margin: 0 auto;
}

.body-outline {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 200px;
    border: 3px solid #ddd;
    border-radius: 40px 40px 20px 20px;
    background: rgba(102, 126, 234, 0.1);
}

.electrode-position {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.electrode-position.ra {
    top: 20px;
    right: 20px;
}

.electrode-position.la {
    top: 20px;
    left: 20px;
}

.electrode-position.rl {
    bottom: 20px;
    right: 20px;
}

.electrode-dot {
    width: 20px;
    height: 20px;
    background: #667eea;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.electrode-position span {
    font-size: 0.8rem;
    font-weight: bold;
    color: #333;
}

/* Component Specifications */
.component-specs {
    padding: 4rem 0;
    background: #f8f9fa;
}

.component-specs h2 {
    text-align: center;
    color: #333;
    font-size: 2.5rem;
    margin-bottom: 3rem;
}

.specs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.spec-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-top: 4px solid #667eea;
}

.spec-card h3 {
    color: #333;
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.spec-card h3 i {
    color: #667eea;
}

.specs-table {
    width: 100%;
    border-collapse: collapse;
}

.specs-table td {
    padding: 0.75rem 0;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: top;
}

.specs-table td:first-child {
    font-weight: 600;
    color: #666;
    width: 50%;
}

.specs-table td:last-child {
    color: #333;
}

.specs-table tr:last-child td {
    border-bottom: none;
}

/* Signal Quality Section */
.signal-quality {
    padding: 4rem 0;
    background: white;
}

.signal-quality h2 {
    text-align: center;
    color: #333;
    font-size: 2.5rem;
    margin-bottom: 3rem;
}

.quality-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.quality-content h3 {
    color: #667eea;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.quality-content h4 {
    color: #333;
    font-size: 1.2rem;
    margin: 1.5rem 0 1rem 0;
}

.quality-content p {
    color: #666;
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.quality-factors {
    list-style: none;
    padding: 0;
}

.quality-factors li {
    margin-bottom: 0.75rem;
    color: #666;
    line-height: 1.6;
}

.quality-factors strong {
    color: #333;
}

.plot-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    padding: 1rem;
    height: 300px;
}

/* Troubleshooting Section */
.troubleshooting {
    padding: 4rem 0;
    background: #f8f9fa;
}

.troubleshooting h2 {
    text-align: center;
    color: #333;
    font-size: 2.5rem;
    margin-bottom: 3rem;
}

.troubleshooting-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.issue-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
}

.issue-header {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.issue-header i {
    font-size: 1.5rem;
}

.issue-header h3 {
    margin: 0;
    font-size: 1.2rem;
}

.issue-content {
    padding: 1.5rem;
}

.issue-content p {
    margin-bottom: 1rem;
    color: #666;
    line-height: 1.6;
}

.issue-content strong {
    color: #333;
}

.issue-content ul {
    margin: 0.5rem 0 0 1rem;
    color: #666;
}

.issue-content li {
    margin-bottom: 0.5rem;
}

/* Component Navigation */
.component-navigation {
    padding: 3rem 0;
    background: white;
    border-top: 1px solid #e9ecef;
}

.nav-links {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.nav-link-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.nav-link-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.nav-link-btn.next {
    background: linear-gradient(135deg, #4CAF50, #45a049);
}

.nav-link-btn.next:hover {
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .overview-grid,
    .quality-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .hero-stats {
        gap: 2rem;
    }
    
    .component-hero h1 {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .nav-links {
        justify-content: center;
        text-align: center;
    }
    
    .nav-link-btn {
        width: 100%;
        justify-content: center;
    }
}
