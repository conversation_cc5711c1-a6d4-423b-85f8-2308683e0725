<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error Management Demo - ECG Signal System</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/component-pages.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .demo-container {
            padding-top: 70px;
            min-height: 100vh;
            background: #f8f9fa;
        }
        
        .demo-content {
            padding: 4rem 0;
        }
        
        .demo-section {
            background: white;
            margin: 2rem 0;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .demo-section h3 {
            color: #667eea;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .demo-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-top: 1rem;
        }
        
        .demo-btn {
            padding: 10px 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .demo-btn:hover {
            background: #764ba2;
            transform: translateY(-2px);
        }
        
        .demo-btn.danger {
            background: #e74c3c;
        }
        
        .demo-btn.danger:hover {
            background: #c0392b;
        }
        
        .demo-btn.warning {
            background: #f39c12;
        }
        
        .demo-btn.warning:hover {
            background: #e67e22;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .feature-list i {
            color: #4CAF50;
            width: 20px;
        }
        
        .error-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-heartbeat"></i>
                <span>ECG Simulator</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="../index.html" class="nav-link">
                        <i class="fas fa-home"></i> Home
                    </a>
                </li>
                <li class="nav-item">
                    <a href="virtual-workbench.html" class="nav-link">
                        <i class="fas fa-tools"></i> Workbench
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#demo" class="nav-link active">
                        <i class="fas fa-bug"></i> Error Demo
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Enhanced Breadcrumb -->
    <nav class="enhanced-breadcrumb">
        <div class="container">
            <div class="breadcrumb-container">
                <div class="breadcrumb">
                    <a href="../index.html" class="breadcrumb-link">
                        <i class="fas fa-home"></i> Home
                    </a>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-current">Error Management Demo</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Demo Content -->
    <div class="demo-container">
        <div class="container">
            <div class="demo-content">
                <h1 class="section-title">Error Management System Demo</h1>
                <p style="text-align: center; color: #666; margin-bottom: 3rem;">
                    Test and explore the comprehensive error handling and recovery system
                </p>

                <!-- Error Handling Features -->
                <div class="demo-section">
                    <h3><i class="fas fa-shield-alt"></i> Error Handling Features</h3>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Automatic error detection and logging</li>
                        <li><i class="fas fa-check"></i> User-friendly error notifications</li>
                        <li><i class="fas fa-check"></i> Automatic recovery attempts</li>
                        <li><i class="fas fa-check"></i> System health monitoring</li>
                        <li><i class="fas fa-check"></i> Error pattern recognition</li>
                        <li><i class="fas fa-check"></i> Contextual error solutions</li>
                    </ul>
                </div>

                <!-- Test Error Types -->
                <div class="demo-section">
                    <h3><i class="fas fa-bug"></i> Test Different Error Types</h3>
                    <p>Click the buttons below to simulate different types of errors and see how the system handles them:</p>
                    <div class="demo-buttons">
                        <button type="button" class="demo-btn danger" onclick="testJavaScriptError()">
                            <i class="fas fa-exclamation-triangle"></i> JavaScript Error
                        </button>
                        <button type="button" class="demo-btn warning" onclick="testNetworkError()">
                            <i class="fas fa-wifi"></i> Network Error
                        </button>
                        <button type="button" class="demo-btn" onclick="testWorkbenchError()">
                            <i class="fas fa-tools"></i> Workbench Error
                        </button>
                        <button type="button" class="demo-btn" onclick="testNavigationError()">
                            <i class="fas fa-compass"></i> Navigation Error
                        </button>
                    </div>
                </div>

                <!-- System Health -->
                <div class="demo-section">
                    <h3><i class="fas fa-heartbeat"></i> System Health Monitoring</h3>
                    <p>Monitor the current system health and error statistics:</p>
                    <div class="demo-buttons">
                        <button type="button" class="demo-btn" onclick="showHealthStatus()">
                            <i class="fas fa-chart-line"></i> Show Health Status
                        </button>
                        <button type="button" class="demo-btn" onclick="showErrorReport()">
                            <i class="fas fa-file-alt"></i> Error Report
                        </button>
                        <button type="button" class="demo-btn warning" onclick="clearAllErrors()">
                            <i class="fas fa-broom"></i> Clear All Errors
                        </button>
                    </div>
                    <div id="health-display" class="error-log" style="display: none;"></div>
                </div>

                <!-- Recovery System -->
                <div class="demo-section">
                    <h3><i class="fas fa-magic"></i> Automatic Recovery</h3>
                    <p>Test the automatic recovery system:</p>
                    <div class="demo-buttons">
                        <button type="button" class="demo-btn" onclick="testAutoRecovery()">
                            <i class="fas fa-magic"></i> Test Auto Recovery
                        </button>
                        <button type="button" class="demo-btn" onclick="simulateSystemIssue()">
                            <i class="fas fa-exclamation"></i> Simulate System Issue
                        </button>
                    </div>
                </div>

                <!-- Error Log -->
                <div class="demo-section">
                    <h3><i class="fas fa-list"></i> Live Error Log</h3>
                    <p>Real-time display of system errors and events:</p>
                    <div id="error-log" class="error-log">
                        <div style="color: #28a745;">System initialized - Error monitoring active</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/error-handler.js"></script>
    <script src="../js/navigation-utils.js"></script>
    <script src="../js/main.js"></script>
    <script>
        // Demo functions for testing error handling
        function testJavaScriptError() {
            try {
                // Intentionally cause a JavaScript error
                nonExistentFunction();
            } catch (error) {
                errorHandler.logErrorWithContext({
                    type: 'JavaScript Error',
                    message: error.message,
                    stack: error.stack
                }, { source: 'Demo Test' });
            }
            logToDisplay('JavaScript error simulated');
        }

        function testNetworkError() {
            errorHandler.logErrorWithContext({
                type: 'Network Error',
                message: 'Failed to fetch resource from server',
                stack: null
            }, { source: 'Demo Test' });
            logToDisplay('Network error simulated');
        }

        function testWorkbenchError() {
            errorHandler.logErrorWithContext({
                type: 'JavaScript Error',
                message: 'Workbench initialization failed - component not found',
                stack: 'Error: Workbench component missing'
            }, { source: 'Demo Test' });
            logToDisplay('Workbench error simulated');
        }

        function testNavigationError() {
            errorHandler.logErrorWithContext({
                type: 'JavaScript Error',
                message: 'Navigation manager undefined - cannot read property',
                stack: 'TypeError: Cannot read property of undefined'
            }, { source: 'Demo Test' });
            logToDisplay('Navigation error simulated');
        }

        function showHealthStatus() {
            const health = errorHandler.performHealthCheck();
            const display = document.getElementById('health-display');
            display.style.display = 'block';
            display.innerHTML = `
                <strong>System Health Report:</strong><br>
                Timestamp: ${health.timestamp}<br>
                Total Errors: ${health.errors}<br>
                Navigation System: ${health.navigation ? '✅ Active' : '❌ Inactive'}<br>
                Workbench System: ${health.workbench ? '✅ Active' : '❌ Inactive'}<br>
                Plotly Library: ${health.plotly ? '✅ Loaded' : '❌ Not Loaded'}<br>
                ${health.memory ? `Memory Usage: ${health.memory.used}MB / ${health.memory.total}MB` : 'Memory info not available'}
            `;
        }

        function showErrorReport() {
            const report = errorHandler.getErrorReport();
            const display = document.getElementById('health-display');
            display.style.display = 'block';
            display.innerHTML = `
                <strong>Error Report:</strong><br>
                Total Errors: ${report.totalErrors}<br>
                URL: ${report.url}<br>
                User Agent: ${report.userAgent}<br>
                <br>
                <strong>Recent Errors:</strong><br>
                ${report.errors.slice(-5).map(error => 
                    `${error.timestamp}: ${error.type} - ${error.message}`
                ).join('<br>')}
            `;
        }

        function clearAllErrors() {
            errorHandler.clearErrors();
            logToDisplay('All errors cleared');
            const display = document.getElementById('health-display');
            display.style.display = 'none';
        }

        function testAutoRecovery() {
            // Simulate an error that triggers auto recovery
            errorHandler.logErrorWithContext({
                type: 'JavaScript Error',
                message: 'Workbench component crashed - attempting recovery',
                stack: 'Error in workbench initialization'
            }, { source: 'Demo Test' });
            logToDisplay('Auto recovery test initiated');
        }

        function simulateSystemIssue() {
            // Generate multiple errors to trigger health warning
            for (let i = 0; i < 12; i++) {
                errorHandler.logError({
                    type: 'System Issue',
                    message: `Simulated system issue #${i + 1}`,
                    timestamp: new Date().toISOString()
                });
            }
            logToDisplay('System issue simulation completed - health warning should appear');
        }

        function logToDisplay(message) {
            const log = document.getElementById('error-log');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `<div style="color: #007bff;">[${timestamp}] ${message}</div>`;
            log.scrollTop = log.scrollHeight;
        }

        // Monitor error handler events
        const originalLogError = errorHandler.logError;
        errorHandler.logError = function(error) {
            originalLogError.call(this, error);
            logToDisplay(`Error logged: ${error.type} - ${error.message}`);
        };
    </script>
</body>
</html>
