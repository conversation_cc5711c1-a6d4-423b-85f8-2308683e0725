/**
 * Interactive Diagrams Styles
 * Enhanced styling for ECG System Interactive Diagrams
 *
 * Author: Dr. <PERSON>il
 * Institution: SUST-BME (Sudan University of Science and Technology - Biomedical Engineering)
 * Year: 2025
 * Copyright: <EMAIL>
 * Contact: +249912867327, +966538076790
 */

/* Main Layout */
.main-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Section */
.diagram-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 0 40px 0;
    text-align: center;
}

.diagram-header h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.diagram-header p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.diagram-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-item i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #ffd700;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Navigation Tabs */
.diagram-navigation {
    background: white;
    border-bottom: 1px solid #e0e0e0;
    padding: 0;
    position: sticky;
    top: 70px;
    z-index: 100;
}

.nav-tabs {
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    overflow-x: auto;
}

.tab-btn {
    padding: 1rem 2rem;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    font-size: 1rem;
    color: #666;
    transition: all 0.3s ease;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tab-btn:hover {
    color: #667eea;
    background: #f8f9ff;
}

.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: #f8f9ff;
}

.tab-btn i {
    font-size: 1.1rem;
}

/* Workspace Layout */
.diagram-workspace {
    flex: 1;
    padding: 2rem 0;
    background: #f8f9fa;
}

.workspace-layout {
    display: grid;
    grid-template-columns: 280px 1fr 320px;
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1rem;
    min-height: 600px;
}

/* Diagram Controls */
.diagram-controls {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    height: fit-content;
    position: sticky;
    top: 150px;
}

.control-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
}

.control-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.control-section h3 {
    font-size: 1rem;
    color: #333;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.control-group {
    margin-bottom: 1rem;
}

.control-group label {
    display: block;
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.control-group input[type="range"] {
    width: 100%;
    margin-bottom: 0.5rem;
}

.control-group span {
    font-size: 0.8rem;
    color: #999;
}

.control-btn {
    width: 100%;
    padding: 0.75rem;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

/* Checkbox Group */
.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.9rem;
    color: #333;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #ddd;
    border-radius: 3px;
    margin-right: 0.75rem;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #667eea;
    border-color: #667eea;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Theme Selector */
.theme-selector {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
}

.theme-btn {
    padding: 0.5rem;
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.theme-btn:hover {
    background: #e9ecef;
}

.theme-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* Export Buttons */
.export-buttons {
    display: flex;
    gap: 0.5rem;
}

.export-btn {
    flex: 1;
    padding: 0.5rem;
    background: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.export-btn:hover {
    background: #218838;
    transform: translateY(-1px);
}

/* Main Diagram Canvas */
.diagram-canvas {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    position: relative;
}

.tab-content {
    display: none;
    padding: 2rem;
    min-height: 600px;
}

.tab-content.active {
    display: block;
}

.diagram-header-info {
    text-align: center;
    margin-bottom: 2rem;
}

.diagram-header-info h2 {
    color: #333;
    margin-bottom: 0.5rem;
}

.diagram-header-info p {
    color: #666;
    font-size: 1rem;
}

.diagram-selector {
    margin-bottom: 2rem;
    text-align: center;
}

.diagram-selector select {
    padding: 0.75rem 1rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 1rem;
    background: white;
    min-width: 250px;
}

.diagram-container {
    background: #fafafa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    min-height: 400px;
    position: relative;
    overflow: hidden;
}

.diagram-container-3d {
    background: #000;
    border-radius: 8px;
    min-height: 500px;
    position: relative;
    overflow: hidden;
}

/* Signal Controls */
.signal-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.signal-selector label {
    margin-right: 0.5rem;
    font-weight: 600;
}

.signal-selector select {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.signal-controls-buttons {
    display: flex;
    gap: 0.5rem;
}

.signal-btn {
    padding: 0.5rem 1rem;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.signal-btn:hover {
    background: #5a6fd8;
}

/* 3D View Controls */
.view-3d-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.view-btn {
    padding: 0.75rem 1.5rem;
    background: #6c757d;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-btn:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

/* Component Info Panel */
.component-info-panel {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    height: fit-content;
    position: sticky;
    top: 150px;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
}

.info-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-header h3 {
    margin: 0;
    color: #333;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.close-info {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    font-size: 1.2rem;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.close-info:hover {
    background: #f5f5f5;
    color: #333;
}

.info-content {
    padding: 1.5rem;
    min-height: 200px;
}

.info-tabs {
    display: flex;
    border-top: 1px solid #e0e0e0;
}

.info-tab {
    flex: 1;
    padding: 0.75rem;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    font-size: 0.9rem;
    color: #666;
    transition: all 0.3s ease;
}

.info-tab:hover {
    background: #f8f9fa;
    color: #333;
}

.info-tab.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: #f8f9ff;
}

.info-tab-content {
    padding: 1.5rem;
}

.info-tab-panel {
    display: none;
}

.info-tab-panel.active {
    display: block;
}

/* Signal Analysis */
.signal-analysis {
    margin-top: 2rem;
}

.analysis-panel {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.analysis-panel h3 {
    margin-bottom: 1rem;
    color: #333;
}

.signal-plots {
    min-height: 300px;
}

/* Signal Flow Diagram Styles */
.signal-flow-diagram {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    padding: 2rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    min-height: 600px;
}

.signal-stage {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #007bff;
}

.signal-stage h4 {
    margin: 0 0 1rem 0;
    color: #333;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.signal-stage h4::before {
    content: '📊';
    font-size: 1.2rem;
}

.signal-plot {
    height: 200px;
    margin: 1rem 0;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background: #fafafa;
}

.signal-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 4px;
    font-size: 0.9rem;
}

.signal-amplitude,
.signal-frequency,
.signal-gain,
.signal-bandwidth,
.signal-snr,
.signal-resolution,
.signal-rate {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.signal-amplitude span,
.signal-frequency span,
.signal-gain span,
.signal-bandwidth span,
.signal-snr span,
.signal-resolution span,
.signal-rate span {
    font-weight: bold;
    color: #007bff;
}

.signal-arrow {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: #666;
    font-size: 1.5rem;
}

.signal-arrow .stage-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #333;
    background: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Component highlighting styles */
.block-component.highlighted {
    filter: drop-shadow(0 0 10px #ffc107);
}

.block-component.highlighted rect {
    stroke: #ffc107;
    stroke-width: 3;
}

/* Theme-specific styles */
.theme-dark .signal-stage {
    background: #2d2d2d;
    border-left-color: #00d4ff;
}

.theme-dark .signal-stage h4 {
    color: #ffffff;
}

.theme-dark .signal-plot {
    background: #1a1a1a;
    border-color: #444;
}

.theme-dark .signal-info {
    background: #333;
    color: #fff;
}

.theme-colorful .signal-stage {
    border-left-color: #ff6600;
}

.theme-colorful .signal-stage:nth-child(odd) {
    border-left-color: #0066cc;
}

.theme-minimal .signal-stage {
    box-shadow: none;
    border: 1px solid #ddd;
    border-left-color: #666;
}

/* Component info panel styles */
.component-overview {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.component-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.component-basic-info h4 {
    margin: 0 0 0.5rem 0;
    color: #333;
}

.component-type {
    font-size: 0.9rem;
    color: #666;
    margin: 0 0 0.5rem 0;
}

.component-description {
    font-size: 0.85rem;
    color: #777;
    margin: 0;
}

.component-quick-specs {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.quick-spec {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem;
    background: white;
    border-radius: 4px;
    border-left: 3px solid #007bff;
}

.spec-label {
    font-weight: 600;
    color: #333;
}

.spec-value {
    color: #666;
}

.specifications-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.spec-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #28a745;
}

.spec-name {
    font-weight: 600;
    color: #333;
}

.spec-value {
    color: #666;
    font-family: monospace;
}

.datasheet-section {
    margin-bottom: 1.5rem;
}

.datasheet-section h5 {
    color: #333;
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

.pin-layout {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.5rem;
    margin-top: 1rem;
}

.pin-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
}

.pin-number {
    width: 24px;
    height: 24px;
    background: #007bff;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
}

.pin-name {
    font-size: 0.9rem;
    color: #333;
}

.calculations-section {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.calculation-item {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #17a2b8;
}

.calculation-item h6 {
    margin: 0 0 0.75rem 0;
    color: #333;
    font-size: 1rem;
}

.formula {
    background: #e9ecef;
    padding: 0.75rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    margin: 0.5rem 0;
    text-align: center;
    border: 1px solid #dee2e6;
}

.no-data {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 2rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .workspace-layout {
        grid-template-columns: 250px 1fr 280px;
        gap: 1rem;
    }
}

@media (max-width: 992px) {
    .workspace-layout {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .diagram-controls,
    .component-info-panel {
        position: static;
        max-height: none;
    }

    .nav-tabs {
        padding: 0 1rem;
    }

    .tab-btn {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 768px) {
    .diagram-header h1 {
        font-size: 2rem;
    }

    .diagram-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .stat-item {
        padding: 1rem;
    }

    .signal-controls {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .view-3d-controls {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .view-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}
