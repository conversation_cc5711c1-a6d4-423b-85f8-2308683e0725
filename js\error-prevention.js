// Error Prevention System
// Comprehensive error prevention and handling for ECG Signal System

class ErrorPrevention {
    constructor() {
        this.isInitialized = false;
        this.safeMode = false;
        this.init();
    }
    
    init() {
        if (this.isInitialized) return;
        
        try {
            this.setupGlobalErrorPrevention();
            this.setupSafeElementAccess();
            this.setupSafeFunctionCalls();
            this.setupResourceLoadingProtection();
            this.preventCommonErrors();
            
            this.isInitialized = true;
            console.log('✅ Error Prevention System initialized');
        } catch (error) {
            console.warn('⚠️ Error Prevention System failed to initialize:', error);
            this.safeMode = true;
        }
    }
    
    setupGlobalErrorPrevention() {
        // Prevent common undefined errors
        window.safeGet = (obj, path, defaultValue = null) => {
            try {
                return path.split('.').reduce((current, key) => current && current[key], obj) || defaultValue;
            } catch {
                return defaultValue;
            }
        };
        
        // Safe element selector
        window.safeQuery = (selector) => {
            try {
                return document.querySelector(selector);
            } catch {
                return null;
            }
        };
        
        // Safe element selector all
        window.safeQueryAll = (selector) => {
            try {
                return document.querySelectorAll(selector);
            } catch {
                return [];
            }
        };
        
        // Safe event listener
        window.safeAddEventListener = (element, event, handler) => {
            try {
                if (element && typeof element.addEventListener === 'function') {
                    element.addEventListener(event, handler);
                    return true;
                }
            } catch {
                // Silent fail
            }
            return false;
        };
    }
    
    setupSafeElementAccess() {
        // Override getElementById to be safe
        const originalGetElementById = document.getElementById;
        document.getElementById = function(id) {
            try {
                return originalGetElementById.call(this, id);
            } catch {
                return null;
            }
        };
        
        // Override querySelector to be safe
        const originalQuerySelector = document.querySelector;
        document.querySelector = function(selector) {
            try {
                return originalQuerySelector.call(this, selector);
            } catch {
                return null;
            }
        };
        
        // Override querySelectorAll to be safe
        const originalQuerySelectorAll = document.querySelectorAll;
        document.querySelectorAll = function(selector) {
            try {
                return originalQuerySelectorAll.call(this, selector);
            } catch {
                return [];
            }
        };
    }
    
    setupSafeFunctionCalls() {
        // Safe function execution
        window.safeExecute = (fn, context = null, ...args) => {
            try {
                if (typeof fn === 'function') {
                    return fn.apply(context, args);
                }
            } catch (error) {
                console.warn('Safe execution failed:', error);
            }
            return null;
        };
        
        // Safe async function execution
        window.safeExecuteAsync = async (fn, context = null, ...args) => {
            try {
                if (typeof fn === 'function') {
                    return await fn.apply(context, args);
                }
            } catch (error) {
                console.warn('Safe async execution failed:', error);
            }
            return null;
        };
    }
    
    setupResourceLoadingProtection() {
        // Prevent script loading errors from breaking the page
        const originalCreateElement = document.createElement;
        document.createElement = function(tagName) {
            const element = originalCreateElement.call(this, tagName);
            
            if (tagName.toLowerCase() === 'script') {
                element.addEventListener('error', (e) => {
                    console.warn('Script loading failed:', e.target.src);
                    // Prevent error propagation
                    e.stopPropagation();
                });
            }
            
            if (tagName.toLowerCase() === 'link') {
                element.addEventListener('error', (e) => {
                    console.warn('CSS loading failed:', e.target.href);
                    // Prevent error propagation
                    e.stopPropagation();
                });
            }
            
            return element;
        };
    }
    
    preventCommonErrors() {
        // Prevent ResizeObserver errors
        if (window.ResizeObserver) {
            const originalObserve = ResizeObserver.prototype.observe;
            ResizeObserver.prototype.observe = function(target) {
                try {
                    return originalObserve.call(this, target);
                } catch (error) {
                    // Silent fail for ResizeObserver errors
                }
            };
        }
        
        // Prevent fetch errors from breaking the page
        if (window.fetch) {
            const originalFetch = window.fetch;
            window.fetch = function(...args) {
                return originalFetch.apply(this, args).catch(error => {
                    console.warn('Fetch failed:', error);
                    return Promise.reject(error);
                });
            };
        }
        
        // Prevent localStorage errors
        window.safeLocalStorage = {
            getItem: (key) => {
                try {
                    return localStorage.getItem(key);
                } catch {
                    return null;
                }
            },
            setItem: (key, value) => {
                try {
                    localStorage.setItem(key, value);
                    return true;
                } catch {
                    return false;
                }
            },
            removeItem: (key) => {
                try {
                    localStorage.removeItem(key);
                    return true;
                } catch {
                    return false;
                }
            }
        };
        
        // Prevent sessionStorage errors
        window.safeSessionStorage = {
            getItem: (key) => {
                try {
                    return sessionStorage.getItem(key);
                } catch {
                    return null;
                }
            },
            setItem: (key, value) => {
                try {
                    sessionStorage.setItem(key, value);
                    return true;
                } catch {
                    return false;
                }
            },
            removeItem: (key) => {
                try {
                    sessionStorage.removeItem(key);
                    return true;
                } catch {
                    return false;
                }
            }
        };
    }
    
    // Safe DOM ready function
    static onReady(callback) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                safeExecute(callback);
            });
        } else {
            safeExecute(callback);
        }
    }
    
    // Safe window load function
    static onLoad(callback) {
        if (document.readyState === 'complete') {
            safeExecute(callback);
        } else {
            window.addEventListener('load', () => {
                safeExecute(callback);
            });
        }
    }
    
    // Check if element exists and is visible
    static isElementReady(selector) {
        const element = safeQuery(selector);
        return element && element.offsetParent !== null;
    }
    
    // Wait for element to be ready
    static waitForElement(selector, timeout = 5000) {
        return new Promise((resolve) => {
            if (ErrorPrevention.isElementReady(selector)) {
                resolve(safeQuery(selector));
                return;
            }
            
            const observer = new MutationObserver(() => {
                if (ErrorPrevention.isElementReady(selector)) {
                    observer.disconnect();
                    resolve(safeQuery(selector));
                }
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
            
            // Timeout fallback
            setTimeout(() => {
                observer.disconnect();
                resolve(null);
            }, timeout);
        });
    }
}

// Initialize error prevention system
const errorPrevention = new ErrorPrevention();

// Export for global access
window.ErrorPrevention = ErrorPrevention;
window.errorPrevention = errorPrevention;

// Add safe initialization helper
window.safeInit = (initFunction) => {
    ErrorPrevention.onReady(() => {
        try {
            initFunction();
        } catch (error) {
            console.warn('Safe initialization failed:', error);
        }
    });
};

console.log('🛡️ Error Prevention System loaded');
