/**
 * Signal Flow Analyzer
 * Advanced signal flow visualization and analysis for ECG System
 * 
 * Author: Dr. <PERSON>
 * Institution: SUST-BME (Sudan University of Science and Technology - Biomedical Engineering)
 * Year: 2025
 * Copyright: <EMAIL>
 * Contact: +249912867327, +966538076790
 * 
 * Features:
 * - Real-time signal flow visualization
 * - ECG signal generation and analysis
 * - Noise simulation and filtering
 * - Interactive signal tracing
 * - Frequency domain analysis
 */

class SignalFlowAnalyzer {
    constructor() {
        this.sampleRate = 256; // Hz
        this.duration = 10; // seconds
        this.timePoints = [];
        this.signalData = new Map();
        this.animationId = null;
        this.currentTime = 0;
        this.isPlaying = false;
        
        this.signalTypes = {
            'ecg-normal': {
                name: 'Normal ECG',
                heartRate: 72,
                amplitude: 1.0,
                noise: 0.02
            },
            'ecg-arrhythmia': {
                name: 'Arrhythmia',
                heartRate: 95,
                amplitude: 0.8,
                noise: 0.03,
                irregular: true
            },
            'noise-50hz': {
                name: '50Hz Noise',
                frequency: 50,
                amplitude: 0.5,
                type: 'sine'
            },
            'baseline-drift': {
                name: 'Baseline Drift',
                frequency: 0.1,
                amplitude: 0.3,
                type: 'sine'
            },
            'muscle-artifact': {
                name: 'Muscle Artifact',
                frequency: 25,
                amplitude: 0.4,
                type: 'random'
            }
        };
        
        this.init();
    }
    
    init() {
        this.generateTimePoints();
        this.setupSignalFlowDiagram();
        this.generateSignalData('ecg-normal');
    }
    
    generateTimePoints() {
        const numPoints = this.sampleRate * this.duration;
        this.timePoints = Array.from({length: numPoints}, (_, i) => i / this.sampleRate);
    }
    
    setupSignalFlowDiagram() {
        const container = document.getElementById('signalFlowContainer');
        if (!container) return;
        
        container.innerHTML = `
            <div class="signal-flow-diagram">
                <div class="signal-stage" id="input-stage">
                    <h4>Input Signal</h4>
                    <div class="signal-plot" id="input-plot"></div>
                    <div class="signal-info">
                        <span class="signal-amplitude">Amplitude: <span id="input-amplitude">0.0mV</span></span>
                        <span class="signal-frequency">Frequency: <span id="input-frequency">0.0Hz</span></span>
                    </div>
                </div>
                
                <div class="signal-arrow">
                    <i class="fas fa-arrow-right"></i>
                    <span class="stage-label">Pre-Amplification</span>
                </div>
                
                <div class="signal-stage" id="preamp-stage">
                    <h4>Pre-Amplified</h4>
                    <div class="signal-plot" id="preamp-plot"></div>
                    <div class="signal-info">
                        <span class="signal-gain">Gain: <span id="preamp-gain">1100x</span></span>
                        <span class="signal-amplitude">Amplitude: <span id="preamp-amplitude">0.0V</span></span>
                    </div>
                </div>
                
                <div class="signal-arrow">
                    <i class="fas fa-arrow-right"></i>
                    <span class="stage-label">Filtering</span>
                </div>
                
                <div class="signal-stage" id="filter-stage">
                    <h4>Filtered Signal</h4>
                    <div class="signal-plot" id="filter-plot"></div>
                    <div class="signal-info">
                        <span class="signal-bandwidth">BW: <span id="filter-bandwidth">0.5-150Hz</span></span>
                        <span class="signal-snr">SNR: <span id="filter-snr">60dB</span></span>
                    </div>
                </div>
                
                <div class="signal-arrow">
                    <i class="fas fa-arrow-right"></i>
                    <span class="stage-label">Digitization</span>
                </div>
                
                <div class="signal-stage" id="digital-stage">
                    <h4>Digital Output</h4>
                    <div class="signal-plot" id="digital-plot"></div>
                    <div class="signal-info">
                        <span class="signal-resolution">Resolution: <span id="digital-resolution">12-bit</span></span>
                        <span class="signal-rate">Rate: <span id="digital-rate">256Hz</span></span>
                    </div>
                </div>
            </div>
        `;
    }
    
    generateSignalData(signalType) {
        const config = this.signalTypes[signalType];
        if (!config) return;
        
        // Clear existing data
        this.signalData.clear();
        
        // Generate input signal
        const inputSignal = this.generateInputSignal(config);
        this.signalData.set('input', inputSignal);
        
        // Generate pre-amplified signal
        const preampSignal = this.applyPreAmplification(inputSignal);
        this.signalData.set('preamp', preampSignal);
        
        // Generate filtered signal
        const filteredSignal = this.applyFiltering(preampSignal);
        this.signalData.set('filtered', filteredSignal);
        
        // Generate digital signal
        const digitalSignal = this.applyDigitization(filteredSignal);
        this.signalData.set('digital', digitalSignal);
        
        // Update plots
        this.updateAllPlots();
    }
    
    generateInputSignal(config) {
        const signal = new Array(this.timePoints.length);
        
        if (config.heartRate) {
            // Generate ECG signal
            for (let i = 0; i < this.timePoints.length; i++) {
                const t = this.timePoints[i];
                signal[i] = this.generateECGWaveform(t, config);
            }
        } else {
            // Generate other signal types
            for (let i = 0; i < this.timePoints.length; i++) {
                const t = this.timePoints[i];
                signal[i] = this.generateTestSignal(t, config);
            }
        }
        
        return signal;
    }
    
    generateECGWaveform(time, config) {
        const heartRate = config.heartRate || 72;
        const period = 60 / heartRate; // seconds per beat
        const t = time % period;
        const normalizedTime = t / period;
        
        let ecg = 0;
        
        // P wave (0.08-0.12s duration)
        if (normalizedTime >= 0.1 && normalizedTime <= 0.2) {
            const pTime = (normalizedTime - 0.1) / 0.1;
            ecg += 0.1 * Math.sin(Math.PI * pTime);
        }
        
        // QRS complex (0.06-0.10s duration)
        if (normalizedTime >= 0.3 && normalizedTime <= 0.4) {
            const qrsTime = (normalizedTime - 0.3) / 0.1;
            if (qrsTime < 0.3) {
                ecg -= 0.2 * Math.sin(Math.PI * qrsTime / 0.3); // Q wave
            } else if (qrsTime < 0.7) {
                ecg += 1.0 * Math.sin(Math.PI * (qrsTime - 0.3) / 0.4); // R wave
            } else {
                ecg -= 0.3 * Math.sin(Math.PI * (qrsTime - 0.7) / 0.3); // S wave
            }
        }
        
        // T wave (0.16s duration)
        if (normalizedTime >= 0.6 && normalizedTime <= 0.8) {
            const tTime = (normalizedTime - 0.6) / 0.2;
            ecg += 0.3 * Math.sin(Math.PI * tTime);
        }
        
        // Add irregularity for arrhythmia
        if (config.irregular) {
            const irregularity = 0.1 * Math.sin(2 * Math.PI * 0.3 * time);
            ecg *= (1 + irregularity);
        }
        
        // Scale amplitude and add noise
        ecg *= config.amplitude;
        ecg += (Math.random() - 0.5) * config.noise;
        
        return ecg * 0.001; // Convert to mV
    }
    
    generateTestSignal(time, config) {
        switch (config.type) {
            case 'sine':
                return config.amplitude * Math.sin(2 * Math.PI * config.frequency * time);
            case 'random':
                return config.amplitude * (Math.random() - 0.5) * Math.sin(2 * Math.PI * config.frequency * time);
            default:
                return 0;
        }
    }
    
    applyPreAmplification(inputSignal) {
        const gain = 1100; // INA128 gain
        return inputSignal.map(sample => sample * gain);
    }
    
    applyFiltering(signal) {
        // Apply high-pass filter (0.5 Hz)
        let filtered = this.highPassFilter(signal, 0.5);
        
        // Apply low-pass filter (150 Hz)
        filtered = this.lowPassFilter(filtered, 150);
        
        // Apply notch filter (50 Hz)
        filtered = this.notchFilter(filtered, 50);
        
        return filtered;
    }
    
    highPassFilter(signal, cutoffFreq) {
        // Simple first-order high-pass filter
        const rc = 1 / (2 * Math.PI * cutoffFreq);
        const dt = 1 / this.sampleRate;
        const alpha = rc / (rc + dt);
        
        const filtered = new Array(signal.length);
        filtered[0] = signal[0];
        
        for (let i = 1; i < signal.length; i++) {
            filtered[i] = alpha * (filtered[i-1] + signal[i] - signal[i-1]);
        }
        
        return filtered;
    }
    
    lowPassFilter(signal, cutoffFreq) {
        // Simple first-order low-pass filter
        const rc = 1 / (2 * Math.PI * cutoffFreq);
        const dt = 1 / this.sampleRate;
        const alpha = dt / (rc + dt);
        
        const filtered = new Array(signal.length);
        filtered[0] = signal[0];
        
        for (let i = 1; i < signal.length; i++) {
            filtered[i] = filtered[i-1] + alpha * (signal[i] - filtered[i-1]);
        }
        
        return filtered;
    }
    
    notchFilter(signal, notchFreq) {
        // Simple notch filter implementation
        const omega = 2 * Math.PI * notchFreq / this.sampleRate;
        const r = 0.95; // Notch depth
        
        const filtered = new Array(signal.length);
        filtered[0] = signal[0];
        filtered[1] = signal[1];
        
        for (let i = 2; i < signal.length; i++) {
            filtered[i] = signal[i] - 2 * r * Math.cos(omega) * filtered[i-1] + r * r * filtered[i-2];
        }
        
        return filtered;
    }
    
    applyDigitization(signal) {
        // 12-bit ADC simulation
        const maxValue = Math.pow(2, 12) - 1; // 4095
        const vRef = 3.3; // Reference voltage
        
        return signal.map(sample => {
            // Clamp to ADC range
            const normalizedSample = Math.max(-vRef/2, Math.min(vRef/2, sample));
            // Quantize
            const digitalValue = Math.round((normalizedSample + vRef/2) / vRef * maxValue);
            // Convert back to voltage
            return (digitalValue / maxValue * vRef) - vRef/2;
        });
    }
    
    updateAllPlots() {
        this.updatePlot('input-plot', this.signalData.get('input'), 'Input Signal');
        this.updatePlot('preamp-plot', this.signalData.get('preamp'), 'Pre-Amplified Signal');
        this.updatePlot('filter-plot', this.signalData.get('filtered'), 'Filtered Signal');
        this.updatePlot('digital-plot', this.signalData.get('digital'), 'Digital Signal');
        
        this.updateSignalInfo();
    }
    
    updatePlot(containerId, data, title) {
        const container = document.getElementById(containerId);
        if (!container || !data || typeof Plotly === 'undefined') return;
        
        const trace = {
            x: this.timePoints,
            y: data,
            type: 'scatter',
            mode: 'lines',
            name: title,
            line: { width: 1.5, color: '#007bff' }
        };
        
        const layout = {
            title: { text: title, font: { size: 12 } },
            xaxis: { title: 'Time (s)', showgrid: true },
            yaxis: { title: 'Amplitude (V)', showgrid: true },
            margin: { t: 30, r: 10, b: 40, l: 50 },
            height: 200,
            showlegend: false
        };
        
        Plotly.newPlot(container, [trace], layout, { responsive: true, displayModeBar: false });
    }
    
    updateSignalInfo() {
        // Update amplitude and frequency information
        const inputData = this.signalData.get('input');
        const preampData = this.signalData.get('preamp');
        const filteredData = this.signalData.get('filtered');
        
        if (inputData) {
            const inputRMS = this.calculateRMS(inputData);
            document.getElementById('input-amplitude').textContent = `${(inputRMS * 1000).toFixed(2)}mV`;
        }
        
        if (preampData) {
            const preampRMS = this.calculateRMS(preampData);
            document.getElementById('preamp-amplitude').textContent = `${preampRMS.toFixed(3)}V`;
        }
        
        if (filteredData) {
            const snr = this.calculateSNR(filteredData);
            document.getElementById('filter-snr').textContent = `${snr.toFixed(1)}dB`;
        }
    }
    
    calculateRMS(signal) {
        const sumSquares = signal.reduce((sum, sample) => sum + sample * sample, 0);
        return Math.sqrt(sumSquares / signal.length);
    }
    
    calculateSNR(signal) {
        // Simple SNR estimation
        const rms = this.calculateRMS(signal);
        const noise = this.estimateNoise(signal);
        return 20 * Math.log10(rms / noise);
    }
    
    estimateNoise(signal) {
        // Estimate noise as high-frequency content
        const diff = signal.slice(1).map((val, i) => val - signal[i]);
        return this.calculateRMS(diff);
    }
    
    playAnimation() {
        this.isPlaying = true;
        this.currentTime = 0;
        this.animateSignalFlow();
    }
    
    pauseAnimation() {
        this.isPlaying = false;
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
    }
    
    resetAnimation() {
        this.pauseAnimation();
        this.currentTime = 0;
        this.updateAnimationMarkers();
    }
    
    animateSignalFlow() {
        if (!this.isPlaying) return;
        
        this.updateAnimationMarkers();
        this.currentTime += 0.1; // Advance by 0.1 seconds
        
        if (this.currentTime >= this.duration) {
            this.currentTime = 0; // Loop animation
        }
        
        this.animationId = requestAnimationFrame(() => this.animateSignalFlow());
    }
    
    updateAnimationMarkers() {
        // Add vertical line markers to show current time position
        const plots = ['input-plot', 'preamp-plot', 'filter-plot', 'digital-plot'];
        
        plots.forEach(plotId => {
            const container = document.getElementById(plotId);
            if (container && typeof Plotly !== 'undefined') {
                const update = {
                    shapes: [{
                        type: 'line',
                        x0: this.currentTime,
                        x1: this.currentTime,
                        y0: 0,
                        y1: 1,
                        yref: 'paper',
                        line: { color: 'red', width: 2 }
                    }]
                };
                
                Plotly.relayout(container, update);
            }
        });
    }
}

// Global functions for HTML onclick handlers
function updateSignalFlow(signalType) {
    if (window.signalFlowAnalyzer) {
        window.signalFlowAnalyzer.generateSignalData(signalType);
    }
}

function playSignalAnimation() {
    if (window.signalFlowAnalyzer) {
        window.signalFlowAnalyzer.playAnimation();
    }
}

function pauseSignalAnimation() {
    if (window.signalFlowAnalyzer) {
        window.signalFlowAnalyzer.pauseAnimation();
    }
}

function resetSignalAnimation() {
    if (window.signalFlowAnalyzer) {
        window.signalFlowAnalyzer.resetAnimation();
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('signalFlowContainer')) {
        window.signalFlowAnalyzer = new SignalFlowAnalyzer();
    }
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SignalFlowAnalyzer;
}
