// Circuit Design Calculators
// Component value calculators for ECG circuit design

// Extend CircuitDesigner with calculator methods
if (typeof CircuitDesigner !== 'undefined') {
    CircuitDesigner.prototype.createGainCalculator = function() {
        return `
            <div class="calculator-section">
                <h3><i class="fas fa-calculator"></i> INA128UA Gain Calculator</h3>
                <div class="calc-inputs">
                    <div class="calc-row">
                        <label for="targetGain">Target Gain:</label>
                        <input type="number" id="targetGain" value="1100" min="1" max="10000" onchange="calculateGainResistor()">
                        <span>x</span>
                    </div>
                    <div class="calc-row">
                        <label for="gainResistor">RG (Gain Resistor):</label>
                        <input type="number" id="gainResistor" value="47" readonly>
                        <span>Ω</span>
                    </div>
                    <div class="calc-row">
                        <label for="actualGain">Actual Gain:</label>
                        <input type="number" id="actualGain" value="1064" readonly>
                        <span>x</span>
                    </div>
                </div>
                <div class="calc-formula">
                    <strong>Formula:</strong> Gain = 1 + (50kΩ / RG)
                    <br><strong>Where:</strong> RG is the external gain-setting resistor
                </div>
                <div class="calc-notes">
                    <h4>Design Notes:</h4>
                    <ul>
                        <li>Use precision metal film resistors (±1% or better)</li>
                        <li>Temperature coefficient: ±25ppm/°C recommended</li>
                        <li>For gains >1000, consider noise and stability</li>
                        <li>Standard E96 resistor values: 47Ω, 49.9Ω, 51.1Ω</li>
                    </ul>
                </div>
            </div>
            <style>
                .calculator-section {
                    background: #f8f9fa;
                    padding: 1.5rem;
                    border-radius: 10px;
                    margin-top: 1rem;
                }
                .calc-inputs {
                    display: grid;
                    gap: 1rem;
                    margin: 1rem 0;
                }
                .calc-row {
                    display: grid;
                    grid-template-columns: 1fr 120px 40px;
                    align-items: center;
                    gap: 0.5rem;
                }
                .calc-row label {
                    font-weight: 600;
                    color: #333;
                }
                .calc-row input {
                    padding: 0.5rem;
                    border: 2px solid #ddd;
                    border-radius: 5px;
                    font-size: 1rem;
                }
                .calc-row input:focus {
                    outline: none;
                    border-color: #667eea;
                }
                .calc-row input[readonly] {
                    background: #e9ecef;
                    color: #495057;
                }
                .calc-formula {
                    background: #e3f2fd;
                    padding: 1rem;
                    border-radius: 5px;
                    margin: 1rem 0;
                    font-family: monospace;
                }
                .calc-notes {
                    background: #fff3e0;
                    padding: 1rem;
                    border-radius: 5px;
                    margin-top: 1rem;
                }
                .calc-notes ul {
                    margin: 0.5rem 0;
                    padding-left: 1.5rem;
                }
                .calc-notes li {
                    margin: 0.25rem 0;
                    font-size: 0.9rem;
                }
            </style>
        `;
    };

    CircuitDesigner.prototype.createFilterCalculator = function() {
        return `
            <div class="calculator-section">
                <h3><i class="fas fa-wave-square"></i> Filter Design Calculator</h3>
                <div class="calc-tabs">
                    <button class="calc-tab active" onclick="showFilterTab('hpf')">High-Pass</button>
                    <button class="calc-tab" onclick="showFilterTab('lpf')">Low-Pass</button>
                    <button class="calc-tab" onclick="showFilterTab('notch')">Notch</button>
                </div>
                
                <div id="hpf-calc" class="calc-tab-content active">
                    <h4>High-Pass Filter (0.5Hz)</h4>
                    <div class="calc-inputs">
                        <div class="calc-row">
                            <label for="hpf_fc">Cutoff Frequency:</label>
                            <input type="number" id="hpf_fc" value="0.5" step="0.1" onchange="calculateHPF()">
                            <span>Hz</span>
                        </div>
                        <div class="calc-row">
                            <label for="hpf_c">Capacitor Value:</label>
                            <input type="number" id="hpf_c" value="1" readonly>
                            <span>μF</span>
                        </div>
                        <div class="calc-row">
                            <label for="hpf_r">Resistor Value:</label>
                            <input type="number" id="hpf_r" value="318" readonly>
                            <span>kΩ</span>
                        </div>
                    </div>
                    <div class="calc-formula">
                        <strong>Formula:</strong> fc = 1 / (2π × R × C)
                    </div>
                </div>
                
                <div id="lpf-calc" class="calc-tab-content">
                    <h4>Low-Pass Filter (150Hz)</h4>
                    <div class="calc-inputs">
                        <div class="calc-row">
                            <label for="lpf_fc">Cutoff Frequency:</label>
                            <input type="number" id="lpf_fc" value="150" onchange="calculateLPF()">
                            <span>Hz</span>
                        </div>
                        <div class="calc-row">
                            <label for="lpf_r">Resistor Value:</label>
                            <input type="number" id="lpf_r" value="10.6" readonly>
                            <span>kΩ</span>
                        </div>
                        <div class="calc-row">
                            <label for="lpf_c">Capacitor Value:</label>
                            <input type="number" id="lpf_c" value="100" readonly>
                            <span>nF</span>
                        </div>
                    </div>
                    <div class="calc-formula">
                        <strong>Formula:</strong> fc = 1 / (2π × R × C)
                    </div>
                </div>
                
                <div id="notch-calc" class="calc-tab-content">
                    <h4>Notch Filter (50Hz)</h4>
                    <div class="calc-inputs">
                        <div class="calc-row">
                            <label for="notch_f0">Center Frequency:</label>
                            <input type="number" id="notch_f0" value="50" onchange="calculateNotch()">
                            <span>Hz</span>
                        </div>
                        <div class="calc-row">
                            <label for="notch_q">Q Factor:</label>
                            <input type="number" id="notch_q" value="30" onchange="calculateNotch()">
                            <span></span>
                        </div>
                        <div class="calc-row">
                            <label for="notch_rejection">Rejection:</label>
                            <input type="number" id="notch_rejection" value="40" readonly>
                            <span>dB</span>
                        </div>
                    </div>
                    <div class="calc-formula">
                        <strong>Formula:</strong> f0 = 1 / (2π√(R1×R2×C1×C2))
                    </div>
                </div>
            </div>
            <style>
                .calc-tabs {
                    display: flex;
                    gap: 0.5rem;
                    margin-bottom: 1rem;
                }
                .calc-tab {
                    padding: 0.5rem 1rem;
                    background: #e9ecef;
                    border: none;
                    border-radius: 5px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                }
                .calc-tab.active {
                    background: #667eea;
                    color: white;
                }
                .calc-tab-content {
                    display: none;
                }
                .calc-tab-content.active {
                    display: block;
                }
            </style>
        `;
    };

    CircuitDesigner.prototype.createPowerCalculator = function() {
        return `
            <div class="calculator-section">
                <h3><i class="fas fa-battery-three-quarters"></i> Power Analysis Calculator</h3>
                <div class="calc-inputs">
                    <div class="calc-row">
                        <label for="supply_voltage">Supply Voltage:</label>
                        <input type="number" id="supply_voltage" value="9" step="0.1" onchange="calculatePower()">
                        <span>V</span>
                    </div>
                    <div class="calc-row">
                        <label for="ina_current">INA128UA Current:</label>
                        <input type="number" id="ina_current" value="700" readonly>
                        <span>μA</span>
                    </div>
                    <div class="calc-row">
                        <label for="opa_current">OPA2131UA Current:</label>
                        <input type="number" id="opa_current" value="900" readonly>
                        <span>μA</span>
                    </div>
                    <div class="calc-row">
                        <label for="total_current">Total Current:</label>
                        <input type="number" id="total_current" value="2.4" readonly>
                        <span>mA</span>
                    </div>
                    <div class="calc-row">
                        <label for="total_power">Total Power:</label>
                        <input type="number" id="total_power" value="43.2" readonly>
                        <span>mW</span>
                    </div>
                    <div class="calc-row">
                        <label for="battery_capacity">Battery Capacity:</label>
                        <input type="number" id="battery_capacity" value="500" onchange="calculatePower()">
                        <span>mAh</span>
                    </div>
                    <div class="calc-row">
                        <label for="battery_life">Battery Life:</label>
                        <input type="number" id="battery_life" value="208" readonly>
                        <span>hours</span>
                    </div>
                </div>
                <div class="power-breakdown">
                    <h4>Power Breakdown:</h4>
                    <div class="power-chart">
                        <div class="power-bar">
                            <div class="power-segment ina" style="width: 29%;">INA128UA (29%)</div>
                            <div class="power-segment opa" style="width: 38%;">OPA2131UA (38%)</div>
                            <div class="power-segment lm358" style="width: 21%;">LM358 (21%)</div>
                            <div class="power-segment op07" style="width: 12%;">OP07D (12%)</div>
                        </div>
                    </div>
                </div>
            </div>
            <style>
                .power-breakdown {
                    margin-top: 1.5rem;
                    background: #f8f9fa;
                    padding: 1rem;
                    border-radius: 5px;
                }
                .power-chart {
                    margin-top: 1rem;
                }
                .power-bar {
                    display: flex;
                    height: 40px;
                    border-radius: 5px;
                    overflow: hidden;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }
                .power-segment {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 0.8rem;
                    font-weight: 600;
                }
                .power-segment.ina { background: #ff9800; }
                .power-segment.opa { background: #2196f3; }
                .power-segment.lm358 { background: #4caf50; }
                .power-segment.op07 { background: #e91e63; }
            </style>
        `;
    };

    CircuitDesigner.prototype.createNoiseCalculator = function() {
        return `
            <div class="calculator-section">
                <h3><i class="fas fa-chart-line"></i> Noise Analysis Calculator</h3>
                <div class="calc-inputs">
                    <div class="calc-row">
                        <label for="ina_noise">INA128UA Noise:</label>
                        <input type="number" id="ina_noise" value="8" readonly>
                        <span>nV/√Hz</span>
                    </div>
                    <div class="calc-row">
                        <label for="thermal_noise">Thermal Noise (1kΩ):</label>
                        <input type="number" id="thermal_noise" value="4" readonly>
                        <span>nV/√Hz</span>
                    </div>
                    <div class="calc-row">
                        <label for="bandwidth">Bandwidth:</label>
                        <input type="number" id="bandwidth" value="150" onchange="calculateNoise()">
                        <span>Hz</span>
                    </div>
                    <div class="calc-row">
                        <label for="total_noise">Total Input Noise:</label>
                        <input type="number" id="total_noise" value="1.1" readonly>
                        <span>μVrms</span>
                    </div>
                    <div class="calc-row">
                        <label for="signal_level">Signal Level:</label>
                        <input type="number" id="signal_level" value="1000" onchange="calculateNoise()">
                        <span>μV</span>
                    </div>
                    <div class="calc-row">
                        <label for="snr">Signal-to-Noise Ratio:</label>
                        <input type="number" id="snr" value="59" readonly>
                        <span>dB</span>
                    </div>
                </div>
                <div class="noise-analysis">
                    <h4>Noise Sources:</h4>
                    <ul>
                        <li><strong>Amplifier Noise:</strong> Voltage and current noise from op-amps</li>
                        <li><strong>Thermal Noise:</strong> Johnson noise from resistors</li>
                        <li><strong>1/f Noise:</strong> Flicker noise at low frequencies</li>
                        <li><strong>Interference:</strong> 50/60Hz power line noise</li>
                    </ul>
                    <h4>Noise Reduction Techniques:</h4>
                    <ul>
                        <li>Use low-noise amplifiers (INA128UA)</li>
                        <li>Minimize source resistance</li>
                        <li>Implement proper shielding</li>
                        <li>Use differential signaling</li>
                        <li>Apply digital filtering</li>
                    </ul>
                </div>
            </div>
        `;
    };
}

// Calculator helper functions
function calculateGainResistor() {
    const targetGain = parseFloat(document.getElementById('targetGain').value);
    const rg = 50000 / (targetGain - 1); // 50kΩ internal resistors
    const actualGain = 1 + (50000 / rg);
    
    document.getElementById('gainResistor').value = rg.toFixed(1);
    document.getElementById('actualGain').value = actualGain.toFixed(0);
}

function calculateHPF() {
    const fc = parseFloat(document.getElementById('hpf_fc').value);
    const c = 1e-6; // 1μF
    const r = 1 / (2 * Math.PI * fc * c);
    
    document.getElementById('hpf_r').value = (r / 1000).toFixed(1); // Convert to kΩ
}

function calculateLPF() {
    const fc = parseFloat(document.getElementById('lpf_fc').value);
    const c = 100e-9; // 100nF
    const r = 1 / (2 * Math.PI * fc * c);
    
    document.getElementById('lpf_r').value = (r / 1000).toFixed(1); // Convert to kΩ
}

function calculateNotch() {
    const f0 = parseFloat(document.getElementById('notch_f0').value);
    const q = parseFloat(document.getElementById('notch_q').value);
    const rejection = 20 * Math.log10(q);
    
    document.getElementById('notch_rejection').value = rejection.toFixed(1);
}

function calculatePower() {
    const voltage = parseFloat(document.getElementById('supply_voltage').value);
    const batteryCapacity = parseFloat(document.getElementById('battery_capacity').value);
    
    // Component currents (typical values)
    const ina_current = 0.7; // mA
    const opa_current = 0.9; // mA
    const lm358_current = 0.5; // mA
    const op07_current = 0.3; // mA
    
    const totalCurrent = ina_current + opa_current + lm358_current + op07_current;
    const totalPower = voltage * totalCurrent;
    const batteryLife = batteryCapacity / totalCurrent;
    
    document.getElementById('total_current').value = totalCurrent.toFixed(1);
    document.getElementById('total_power').value = totalPower.toFixed(1);
    document.getElementById('battery_life').value = batteryLife.toFixed(0);
}

function calculateNoise() {
    const bandwidth = parseFloat(document.getElementById('bandwidth').value);
    const signalLevel = parseFloat(document.getElementById('signal_level').value);
    
    // Noise calculations
    const ina_noise = 8e-9; // 8 nV/√Hz
    const thermal_noise = 4e-9; // 4 nV/√Hz
    
    const totalNoise = Math.sqrt(Math.pow(ina_noise, 2) + Math.pow(thermal_noise, 2)) * Math.sqrt(bandwidth);
    const snr = 20 * Math.log10(signalLevel * 1e-6 / totalNoise);
    
    document.getElementById('total_noise').value = (totalNoise * 1e6).toFixed(1); // Convert to μV
    document.getElementById('snr').value = snr.toFixed(0);
}

function showFilterTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.calc-tab-content').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // Remove active class from all tabs
    document.querySelectorAll('.calc-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // Show selected tab content
    document.getElementById(tabName + '-calc').classList.add('active');
    
    // Add active class to clicked tab
    event.target.classList.add('active');
}
