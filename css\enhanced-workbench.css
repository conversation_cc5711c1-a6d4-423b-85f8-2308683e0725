/* Enhanced Virtual Workbench Styles */

/* Component Library Enhancements */
.component-item {
    display: flex;
    align-items: center;
    padding: 8px;
    margin: 4px 0;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background: white;
    cursor: grab;
    transition: all 0.2s ease;
    position: relative;
}

.component-item:hover {
    background: #f5f5f5;
    border-color: #2196F3;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.component-item.dragging {
    opacity: 0.7;
    transform: rotate(5deg);
    cursor: grabbing;
}

.component-icon {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    margin-right: 8px;
    flex-shrink: 0;
}

.component-info {
    flex: 1;
    min-width: 0;
}

.component-name {
    font-weight: 600;
    font-size: 12px;
    color: #333;
    margin-bottom: 2px;
}

.component-desc {
    font-size: 10px;
    color: #666;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.component-pins {
    font-size: 9px;
    color: #999;
}

.component-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.component-item:hover .component-actions {
    opacity: 1;
}

.action-btn {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 3px;
    background: #f0f0f0;
    color: #666;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: #2196F3;
    color: white;
}

/* Canvas Enhancements */
.design-canvas {
    position: relative;
    overflow: hidden;
    background: #fafafa;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.canvas-content {
    position: relative;
    width: 100%;
    height: 100%;
}

#mainCanvas, #overlayCanvas {
    display: block;
    border-radius: 4px;
}

/* Toolbar Enhancements */
.toolbar-section h4 {
    font-size: 11px;
    color: #666;
    margin: 0 0 8px 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.toolbar-btn.active {
    background: #2196F3;
    color: white;
    box-shadow: 0 2px 4px rgba(33, 150, 243, 0.3);
}

.simulation-btn {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    font-weight: 600;
}

.simulation-btn:hover {
    background: linear-gradient(135deg, #45a049, #4CAF50);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
}

.simulation-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 11px;
    color: #666;
    margin-top: 8px;
    padding: 4px 8px;
    background: #f5f5f5;
    border-radius: 4px;
}

.zoom-display {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 8px;
    padding: 4px 8px;
    background: #f5f5f5;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    color: #666;
}

/* Properties Panel Enhancements */
.property-section {
    margin-bottom: 20px;
    padding: 12px;
    background: #f9f9f9;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

.property-section h4 {
    margin: 0 0 12px 0;
    font-size: 13px;
    color: #333;
    font-weight: 600;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 6px;
}

.property-item {
    margin-bottom: 10px;
}

.property-item label {
    display: block;
    font-size: 11px;
    color: #666;
    margin-bottom: 4px;
    font-weight: 500;
}

.property-item input,
.property-item select {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 12px;
    background: white;
    transition: border-color 0.2s ease;
}

.property-item input:focus,
.property-item select:focus {
    outline: none;
    border-color: #2196F3;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

/* Status Bar Enhancements */
.status-bar {
    background: linear-gradient(135deg, #f5f5f5, #eeeeee);
    border-top: 1px solid #ddd;
    padding: 8px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 11px;
    color: #666;
}

.status-section {
    display: flex;
    gap: 20px;
    align-items: center;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    background: white;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
}

.status-item i {
    font-size: 10px;
    color: #999;
}

/* Simulation Results */
.simulation-results {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    max-width: 1000px;
    height: 70%;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    z-index: 1000;
    display: none;
}

.simulation-results.active {
    display: block;
}

.results-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.results-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
}

.results-close {
    background: none;
    border: none;
    font-size: 20px;
    color: #999;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.results-close:hover {
    background: #f0f0f0;
    color: #333;
}

.results-content {
    padding: 20px;
    height: calc(100% - 70px);
    overflow-y: auto;
}

.results-tabs {
    display: flex;
    gap: 4px;
    margin-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
}

.results-tab {
    padding: 8px 16px;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    font-size: 13px;
    color: #666;
    transition: all 0.2s ease;
}

.results-tab.active {
    color: #2196F3;
    border-bottom-color: #2196F3;
}

.results-tab:hover {
    background: #f5f5f5;
}

/* Measurement Tool Displays */
.measurement-display {
    background: #1a1a1a;
    border-radius: 8px;
    padding: 16px;
    margin: 12px 0;
    color: #00ff00;
    font-family: 'Courier New', monospace;
    border: 2px solid #333;
    position: relative;
}

.measurement-display::before {
    content: '';
    position: absolute;
    top: 8px;
    right: 8px;
    width: 8px;
    height: 8px;
    background: #00ff00;
    border-radius: 50%;
    box-shadow: 0 0 10px #00ff00;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}

.measurement-title {
    font-size: 14px;
    color: #00ff00;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.measurement-value {
    font-size: 24px;
    font-weight: bold;
    color: #00ff00;
    text-align: center;
    padding: 8px;
    background: rgba(0, 255, 0, 0.1);
    border-radius: 4px;
    margin: 8px 0;
}

.measurement-controls {
    display: flex;
    gap: 8px;
    margin-top: 12px;
}

.measurement-btn {
    padding: 4px 8px;
    background: #333;
    border: 1px solid #555;
    color: #00ff00;
    border-radius: 3px;
    cursor: pointer;
    font-size: 10px;
    transition: all 0.2s ease;
}

.measurement-btn:hover {
    background: #555;
    border-color: #00ff00;
}

/* Component Highlighting */
.component-highlight {
    position: absolute;
    pointer-events: none;
    border: 2px dashed #FF4081;
    border-radius: 4px;
    background: rgba(255, 64, 129, 0.1);
    animation: highlight-pulse 1s infinite;
}

@keyframes highlight-pulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 0.3; }
}

/* Wire Drawing */
.wire-preview {
    stroke: #2196F3;
    stroke-width: 2;
    stroke-dasharray: 5, 5;
    fill: none;
    pointer-events: none;
}

.connection-point {
    fill: #FFD700;
    stroke: #333;
    stroke-width: 1;
    r: 3;
}

.connection-point:hover {
    fill: #FF4081;
    r: 4;
}

/* Grid Styles */
.grid-pattern {
    stroke: #e0e0e0;
    stroke-width: 0.5;
    fill: none;
}

.grid-major {
    stroke: #d0d0d0;
    stroke-width: 1;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .component-library {
        width: 200px;
    }

    .properties-panel {
        width: 200px;
    }

    .component-name {
        font-size: 11px;
    }

    .component-desc {
        font-size: 9px;
    }
}

@media (max-width: 768px) {
    .workbench-main {
        flex-direction: column;
    }

    .component-library,
    .properties-panel {
        width: 100%;
        height: 200px;
        overflow-y: auto;
    }

    .design-canvas-container {
        height: 400px;
    }

    .simulation-results {
        width: 95%;
        height: 80%;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #2196F3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Author Information Footer */
.author-footer {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    padding: 16px 20px;
    border-top: 3px solid #3498db;
    margin-top: auto;
}

.author-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.author-details h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    color: #3498db;
    font-weight: 600;
}

.author-details p,
.contact-info p {
    margin: 4px 0;
    font-size: 12px;
    color: #bdc3c7;
    display: flex;
    align-items: center;
    gap: 8px;
}

.author-details i,
.contact-info i {
    color: #3498db;
    width: 14px;
    text-align: center;
}

.contact-info {
    text-align: right;
}

.contact-info p {
    justify-content: flex-end;
}

/* Responsive Author Footer */
@media (max-width: 768px) {
    .author-info {
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }

    .contact-info {
        text-align: center;
    }

    .contact-info p {
        justify-content: center;
    }

    .author-details p,
    .contact-info p {
        font-size: 11px;
    }
}
