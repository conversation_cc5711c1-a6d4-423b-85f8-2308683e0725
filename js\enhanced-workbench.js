// Enhanced Virtual Workbench with Advanced Features
// Circuit Drawing, Simulation, Connectivity, and Measurement Tools

class EnhancedWorkbench {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.components = new Map();
        this.connections = new Map();
        this.selectedComponent = null;
        this.currentTool = 'select';
        this.isDrawing = false;
        this.zoom = 1.0;
        this.gridSize = 20;
        this.showGrid = true;
        this.simulationRunning = false;
        this.measurementTools = new Map();

        this.init();
    }

    init() {
        try {
            this.setupCanvas();
            this.loadComponentLibrary();
            this.setupEventListeners();
            this.setupMeasurementTools();
            this.initializeSimulation();

            console.log('✅ Enhanced Workbench initialized');
        } catch (error) {
            console.error('❌ Enhanced Workbench initialization failed:', error);
        }
    }

    setupCanvas() {
        const canvasContainer = document.getElementById('canvasContent');
        if (!canvasContainer) return;

        // Create main drawing canvas
        this.canvas = document.createElement('canvas');
        this.canvas.id = 'mainCanvas';
        this.canvas.width = 1200;
        this.canvas.height = 800;
        this.ctx = this.canvas.getContext('2d');

        // Create overlay canvas for UI elements
        this.overlayCanvas = document.createElement('canvas');
        this.overlayCanvas.id = 'overlayCanvas';
        this.overlayCanvas.width = 1200;
        this.overlayCanvas.height = 800;
        this.overlayCtx = this.overlayCanvas.getContext('2d');

        // Style canvases
        this.canvas.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            border: 1px solid #ddd;
            background: white;
            cursor: crosshair;
        `;

        this.overlayCanvas.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            pointer-events: none;
            z-index: 10;
        `;

        canvasContainer.appendChild(this.canvas);
        canvasContainer.appendChild(this.overlayCanvas);

        this.drawGrid();
    }

    loadComponentLibrary() {
        this.componentLibrary = {
            amplifiers: [
                {
                    id: 'INA128UA',
                    name: 'INA128UA',
                    category: 'amplifiers',
                    description: 'Precision Instrumentation Amplifier',
                    pins: 8,
                    pinout: ['RG', 'IN-', 'IN+', 'V-', 'REF', 'OUT', 'V+', 'RG'],
                    specs: {
                        gain: '1-10000',
                        cmrr: '120dB',
                        bandwidth: '200kHz',
                        supply: '±2.25V to ±18V'
                    },
                    symbol: 'triangle',
                    color: '#4CAF50'
                },
                {
                    id: 'OPA2131UA',
                    name: 'OPA2131UA',
                    category: 'amplifiers',
                    description: 'Dual Low-Noise Op-Amp',
                    pins: 8,
                    pinout: ['OUT1', 'IN1-', 'IN1+', 'V-', 'IN2+', 'IN2-', 'OUT2', 'V+'],
                    specs: {
                        noise: '8nV/√Hz',
                        bandwidth: '4MHz',
                        slewRate: '20V/μs',
                        supply: '±2.25V to ±18V'
                    },
                    symbol: 'triangle',
                    color: '#2196F3'
                },
                {
                    id: 'LM358',
                    name: 'LM358',
                    category: 'amplifiers',
                    description: 'Dual Op-Amp',
                    pins: 8,
                    pinout: ['OUT1', 'IN1-', 'IN1+', 'VCC', 'IN2+', 'IN2-', 'OUT2', 'GND'],
                    specs: {
                        bandwidth: '1MHz',
                        supply: '3V to 32V',
                        inputOffset: '2mV',
                        slewRate: '0.5V/μs'
                    },
                    symbol: 'triangle',
                    color: '#FF9800'
                },
                {
                    id: 'OP07D',
                    name: 'OP07D',
                    category: 'amplifiers',
                    description: 'Ultra-Low Offset Op-Amp',
                    pins: 8,
                    pinout: ['NC', 'IN-', 'IN+', 'V-', 'NC', 'OUT', 'V+', 'NC'],
                    specs: {
                        inputOffset: '25μV',
                        driftOffset: '0.6μV/°C',
                        bandwidth: '600kHz',
                        supply: '±3V to ±18V'
                    },
                    symbol: 'triangle',
                    color: '#E91E63'
                }
            ],
            passives: [
                {
                    id: 'RESISTOR',
                    name: 'Resistor',
                    category: 'passives',
                    description: 'Fixed Resistor',
                    pins: 2,
                    pinout: ['1', '2'],
                    specs: {
                        tolerance: '±1%, ±5%',
                        power: '1/8W, 1/4W, 1/2W',
                        tempCoeff: '±25ppm/°C',
                        packages: 'R0603, R0805, R1206'
                    },
                    symbol: 'rectangle',
                    color: '#795548'
                },
                {
                    id: 'CAPACITOR',
                    name: 'Capacitor',
                    category: 'passives',
                    description: 'Fixed Capacitor',
                    pins: 2,
                    pinout: ['+', '-'],
                    specs: {
                        types: 'Ceramic, Electrolytic, Tantalum',
                        voltage: '6.3V to 450V',
                        tolerance: '±5%, ±10%, ±20%',
                        packages: 'C0603, C0805, C1206'
                    },
                    symbol: 'capacitor',
                    color: '#607D8B'
                },
                {
                    id: 'INDUCTOR',
                    name: 'Inductor',
                    category: 'passives',
                    description: 'Fixed Inductor',
                    pins: 2,
                    pinout: ['1', '2'],
                    specs: {
                        inductance: '1nH to 1H',
                        current: '100mA to 10A',
                        tolerance: '±5%, ±10%, ±20%',
                        packages: 'L0603, L0805, L1206'
                    },
                    symbol: 'inductor',
                    color: '#9C27B0'
                }
            ],
            power: [
                {
                    id: 'A0509S',
                    name: 'A0509S-1WR3',
                    category: 'power',
                    description: 'Isolated DC-DC Converter',
                    pins: 4,
                    pinout: ['VIN+', 'VIN-', 'VOUT+', 'VOUT-'],
                    specs: {
                        inputVoltage: '4.5V to 5.5V',
                        outputVoltage: '±9V',
                        power: '1W',
                        isolation: '1500VDC'
                    },
                    symbol: 'rectangle',
                    color: '#FFC107'
                },
                {
                    id: 'LM7805',
                    name: 'LM7805',
                    category: 'power',
                    description: '5V Linear Regulator',
                    pins: 3,
                    pinout: ['VIN', 'GND', 'VOUT'],
                    specs: {
                        inputVoltage: '7V to 35V',
                        outputVoltage: '5V',
                        current: '1A',
                        dropout: '2V'
                    },
                    symbol: 'rectangle',
                    color: '#FF5722'
                }
            ],
            connectors: [
                {
                    id: 'ELECTRODE',
                    name: 'ECG Electrode',
                    category: 'connectors',
                    description: 'Ag/AgCl Electrode',
                    pins: 1,
                    pinout: ['SIGNAL'],
                    specs: {
                        material: 'Ag/AgCl',
                        impedance: '<2kΩ',
                        offset: '<100μV',
                        size: '24mm diameter'
                    },
                    symbol: 'circle',
                    color: '#FF6B6B'
                },
                {
                    id: 'CONNECTOR_2',
                    name: '2-Pin Connector',
                    category: 'connectors',
                    description: 'Generic 2-Pin Connector',
                    pins: 2,
                    pinout: ['1', '2'],
                    specs: {
                        current: '2A',
                        voltage: '250V',
                        pitch: '2.54mm',
                        mounting: 'Through-hole'
                    },
                    symbol: 'connector',
                    color: '#9E9E9E'
                }
            ],
            digital: [
                {
                    id: 'STM32F4',
                    name: 'STM32F407VGT6',
                    category: 'digital',
                    description: 'ARM Cortex-M4 Microcontroller',
                    pins: 100,
                    pinout: ['Various GPIO, ADC, DAC, UART, SPI, I2C'],
                    specs: {
                        core: 'ARM Cortex-M4',
                        frequency: '168MHz',
                        flash: '1MB',
                        ram: '192KB',
                        adc: '12-bit, 2.4MSPS'
                    },
                    symbol: 'rectangle',
                    color: '#3F51B5'
                },
                {
                    id: 'ARDUINO_UNO',
                    name: 'Arduino Uno',
                    category: 'digital',
                    description: 'Arduino Development Board',
                    pins: 30,
                    pinout: ['Digital I/O', 'Analog Input', 'Power'],
                    specs: {
                        microcontroller: 'ATmega328P',
                        frequency: '16MHz',
                        digitalIO: '14 pins',
                        analogInput: '6 pins'
                    },
                    symbol: 'rectangle',
                    color: '#00BCD4'
                }
            ],
            measurement: [
                {
                    id: 'OSCILLOSCOPE',
                    name: 'Digital Oscilloscope',
                    category: 'measurement',
                    description: 'Virtual Oscilloscope',
                    pins: 4,
                    pinout: ['CH1', 'CH2', 'GND', 'TRIG'],
                    specs: {
                        channels: '2',
                        bandwidth: '100MHz',
                        sampleRate: '1GSa/s',
                        resolution: '8-bit'
                    },
                    symbol: 'oscilloscope',
                    color: '#4CAF50'
                },
                {
                    id: 'MULTIMETER',
                    name: 'Digital Multimeter',
                    category: 'measurement',
                    description: 'Virtual Multimeter',
                    pins: 3,
                    pinout: ['V/Ω', 'COM', 'A'],
                    specs: {
                        dcVoltage: '200mV to 1000V',
                        acVoltage: '200mV to 750V',
                        resistance: '200Ω to 20MΩ',
                        current: '200μA to 10A'
                    },
                    symbol: 'multimeter',
                    color: '#FF9800'
                },
                {
                    id: 'FUNCTION_GEN',
                    name: 'Function Generator',
                    category: 'measurement',
                    description: 'Virtual Function Generator',
                    pins: 2,
                    pinout: ['OUT', 'GND'],
                    specs: {
                        frequency: '1mHz to 25MHz',
                        amplitude: '1mVpp to 10Vpp',
                        waveforms: 'Sine, Square, Triangle, Pulse',
                        accuracy: '±1ppm'
                    },
                    symbol: 'function_gen',
                    color: '#2196F3'
                }
            ]
        };

        this.populateComponentList('amplifiers');
    }

    populateComponentList(category) {
        const componentList = document.getElementById('componentList');
        if (!componentList) return;

        const components = this.componentLibrary[category] || [];
        componentList.innerHTML = '';

        components.forEach(component => {
            const componentItem = document.createElement('div');
            componentItem.className = 'component-item';
            componentItem.draggable = true;
            componentItem.dataset.componentId = component.id;

            componentItem.innerHTML = `
                <div class="component-icon" style="background-color: ${component.color}">
                    <i class="fas fa-microchip"></i>
                </div>
                <div class="component-info">
                    <div class="component-name">${component.name}</div>
                    <div class="component-desc">${component.description}</div>
                    <div class="component-pins">${component.pins} pins</div>
                </div>
                <div class="component-actions">
                    <button type="button" class="action-btn" onclick="previewComponent('${component.id}')" title="Preview">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button type="button" class="action-btn" onclick="addComponentToCanvas('${component.id}')" title="Add">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            `;

            // Add drag and drop functionality
            componentItem.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('text/plain', component.id);
                e.dataTransfer.effectAllowed = 'copy';
            });

            componentList.appendChild(componentItem);
        });
    }

    setupEventListeners() {
        // Canvas mouse events
        this.canvas.addEventListener('mousedown', this.handleMouseDown.bind(this));
        this.canvas.addEventListener('mousemove', this.handleMouseMove.bind(this));
        this.canvas.addEventListener('mouseup', this.handleMouseUp.bind(this));
        this.canvas.addEventListener('wheel', this.handleWheel.bind(this));

        // Canvas drop events
        this.canvas.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'copy';
        });

        this.canvas.addEventListener('drop', this.handleDrop.bind(this));

        // Keyboard events
        document.addEventListener('keydown', this.handleKeyDown.bind(this));

        // Window resize
        window.addEventListener('resize', this.handleResize.bind(this));
    }

    setupMeasurementTools() {
        this.measurementTools.set('oscilloscope', {
            active: false,
            channels: ['CH1', 'CH2'],
            timebase: '1ms/div',
            voltage: '1V/div',
            trigger: 'auto',
            data: new Map()
        });

        this.measurementTools.set('multimeter', {
            active: false,
            mode: 'DC_VOLTAGE',
            range: 'auto',
            value: 0,
            unit: 'V'
        });

        this.measurementTools.set('function_generator', {
            active: false,
            frequency: 1000,
            amplitude: 1,
            waveform: 'sine',
            offset: 0
        });
    }

    // Circuit Drawing Methods
    drawGrid() {
        if (!this.showGrid) return;

        this.ctx.save();
        this.ctx.strokeStyle = '#e0e0e0';
        this.ctx.lineWidth = 0.5;

        const width = this.canvas.width;
        const height = this.canvas.height;

        // Draw vertical lines
        for (let x = 0; x <= width; x += this.gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, height);
            this.ctx.stroke();
        }

        // Draw horizontal lines
        for (let y = 0; y <= height; y += this.gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(width, y);
            this.ctx.stroke();
        }

        this.ctx.restore();
    }

    drawComponent(component) {
        const ctx = this.ctx;
        ctx.save();

        ctx.translate(component.x, component.y);
        ctx.rotate(component.rotation * Math.PI / 180);

        // Draw component based on type
        switch (component.symbol) {
            case 'triangle':
                this.drawOpAmp(ctx, component);
                break;
            case 'rectangle':
                this.drawRectangularComponent(ctx, component);
                break;
            case 'capacitor':
                this.drawCapacitor(ctx, component);
                break;
            case 'inductor':
                this.drawInductor(ctx, component);
                break;
            case 'circle':
                this.drawCircularComponent(ctx, component);
                break;
            case 'oscilloscope':
                this.drawOscilloscope(ctx, component);
                break;
            case 'multimeter':
                this.drawMultimeter(ctx, component);
                break;
            case 'function_gen':
                this.drawFunctionGenerator(ctx, component);
                break;
            default:
                this.drawGenericComponent(ctx, component);
        }

        // Draw component label
        ctx.fillStyle = '#333';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(component.name, 0, -25);

        // Draw pins
        this.drawComponentPins(ctx, component);

        ctx.restore();
    }

    drawOpAmp(ctx, component) {
        ctx.fillStyle = component.color || '#4CAF50';
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 2;

        // Draw triangle
        ctx.beginPath();
        ctx.moveTo(-20, -15);
        ctx.lineTo(-20, 15);
        ctx.lineTo(20, 0);
        ctx.closePath();
        ctx.fill();
        ctx.stroke();

        // Draw + and - symbols
        ctx.fillStyle = 'white';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('+', -10, -5);
        ctx.fillText('-', -10, 10);
    }

    drawRectangularComponent(ctx, component) {
        ctx.fillStyle = component.color || '#607D8B';
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 2;

        const width = component.width || 40;
        const height = component.height || 20;

        ctx.fillRect(-width/2, -height/2, width, height);
        ctx.strokeRect(-width/2, -height/2, width, height);

        // Draw component type text
        ctx.fillStyle = 'white';
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(component.type || 'IC', 0, 3);
    }

    drawCapacitor(ctx, component) {
        ctx.strokeStyle = component.color || '#607D8B';
        ctx.lineWidth = 3;

        // Draw two parallel lines
        ctx.beginPath();
        ctx.moveTo(-5, -15);
        ctx.lineTo(-5, 15);
        ctx.moveTo(5, -15);
        ctx.lineTo(5, 15);
        ctx.stroke();

        // Draw connection lines
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(-20, 0);
        ctx.lineTo(-5, 0);
        ctx.moveTo(5, 0);
        ctx.lineTo(20, 0);
        ctx.stroke();
    }

    drawInductor(ctx, component) {
        ctx.strokeStyle = component.color || '#9C27B0';
        ctx.lineWidth = 2;
        ctx.fillStyle = 'none';

        // Draw coil
        const coils = 4;
        const coilWidth = 8;

        ctx.beginPath();
        ctx.moveTo(-20, 0);
        ctx.lineTo(-coils * coilWidth / 2, 0);

        for (let i = 0; i < coils; i++) {
            const x = -coils * coilWidth / 2 + i * coilWidth;
            ctx.arc(x + coilWidth / 2, 0, coilWidth / 2, Math.PI, 0, false);
        }

        ctx.lineTo(20, 0);
        ctx.stroke();
    }

    drawCircularComponent(ctx, component) {
        ctx.fillStyle = component.color || '#FF6B6B';
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 2;

        const radius = component.radius || 15;

        ctx.beginPath();
        ctx.arc(0, 0, radius, 0, 2 * Math.PI);
        ctx.fill();
        ctx.stroke();

        // Draw connection point
        ctx.fillStyle = '#333';
        ctx.beginPath();
        ctx.arc(0, 0, 3, 0, 2 * Math.PI);
        ctx.fill();
    }

    drawOscilloscope(ctx, component) {
        ctx.fillStyle = '#2E2E2E';
        ctx.strokeStyle = '#4CAF50';
        ctx.lineWidth = 2;

        // Draw oscilloscope body
        ctx.fillRect(-30, -20, 60, 40);
        ctx.strokeRect(-30, -20, 60, 40);

        // Draw screen
        ctx.fillStyle = '#000';
        ctx.fillRect(-25, -15, 50, 25);

        // Draw waveform
        ctx.strokeStyle = '#4CAF50';
        ctx.lineWidth = 1;
        ctx.beginPath();
        for (let x = -25; x <= 25; x += 2) {
            const y = Math.sin(x * 0.2) * 5;
            if (x === -25) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        }
        ctx.stroke();

        // Draw label
        ctx.fillStyle = '#4CAF50';
        ctx.font = '8px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('OSC', 0, 18);
    }

    drawMultimeter(ctx, component) {
        ctx.fillStyle = '#FFB74D';
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 2;

        // Draw multimeter body
        ctx.fillRect(-25, -30, 50, 60);
        ctx.strokeRect(-25, -30, 50, 60);

        // Draw display
        ctx.fillStyle = '#000';
        ctx.fillRect(-20, -25, 40, 15);

        // Draw display text
        ctx.fillStyle = '#0F0';
        ctx.font = '10px monospace';
        ctx.textAlign = 'center';
        ctx.fillText('0.000V', 0, -15);

        // Draw selector dial
        ctx.strokeStyle = '#333';
        ctx.beginPath();
        ctx.arc(0, 5, 10, 0, 2 * Math.PI);
        ctx.stroke();

        // Draw label
        ctx.fillStyle = '#333';
        ctx.font = '8px Arial';
        ctx.fillText('DMM', 0, 25);
    }

    drawFunctionGenerator(ctx, component) {
        ctx.fillStyle = '#64B5F6';
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 2;

        // Draw function generator body
        ctx.fillRect(-30, -20, 60, 40);
        ctx.strokeRect(-30, -20, 60, 40);

        // Draw waveform display
        ctx.strokeStyle = '#1976D2';
        ctx.lineWidth = 2;
        ctx.beginPath();
        for (let x = -25; x <= 25; x += 2) {
            const y = Math.sin(x * 0.3) * 8;
            if (x === -25) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        }
        ctx.stroke();

        // Draw label
        ctx.fillStyle = '#333';
        ctx.font = '8px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('FGEN', 0, 18);
    }

    drawGenericComponent(ctx, component) {
        ctx.fillStyle = component.color || '#9E9E9E';
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 2;

        ctx.fillRect(-15, -10, 30, 20);
        ctx.strokeRect(-15, -10, 30, 20);

        ctx.fillStyle = 'white';
        ctx.font = '8px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(component.type || '?', 0, 3);
    }

    drawComponentPins(ctx, component) {
        const pinPositions = this.getComponentPinPositions(component);

        ctx.fillStyle = '#FFD700';
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 1;

        pinPositions.forEach((pin, index) => {
            ctx.beginPath();
            ctx.arc(pin.x, pin.y, 3, 0, 2 * Math.PI);
            ctx.fill();
            ctx.stroke();

            // Draw pin number
            ctx.fillStyle = '#333';
            ctx.font = '8px Arial';
            ctx.textAlign = 'center';
            ctx.fillText((index + 1).toString(), pin.x, pin.y - 8);
        });
    }

    getComponentPinPositions(component) {
        const pins = [];
        const pinCount = component.pins || 2;

        // Calculate pin positions based on component type
        switch (component.symbol) {
            case 'triangle': // Op-amp
                pins.push({x: -25, y: -8}); // IN-
                pins.push({x: -25, y: 8});  // IN+
                pins.push({x: 25, y: 0});   // OUT
                if (pinCount > 3) {
                    pins.push({x: 0, y: -20}); // V+
                    pins.push({x: 0, y: 20});  // V-
                }
                break;

            case 'capacitor':
            case 'inductor':
                pins.push({x: -20, y: 0});
                pins.push({x: 20, y: 0});
                break;

            case 'rectangle':
                // Distribute pins around rectangle
                const pinsPerSide = Math.ceil(pinCount / 4);
                for (let i = 0; i < pinCount; i++) {
                    const side = Math.floor(i / pinsPerSide);
                    const posOnSide = i % pinsPerSide;

                    switch (side) {
                        case 0: // Top
                            pins.push({x: -15 + (30 * posOnSide / (pinsPerSide - 1)), y: -15});
                            break;
                        case 1: // Right
                            pins.push({x: 15, y: -10 + (20 * posOnSide / (pinsPerSide - 1))});
                            break;
                        case 2: // Bottom
                            pins.push({x: 15 - (30 * posOnSide / (pinsPerSide - 1)), y: 15});
                            break;
                        case 3: // Left
                            pins.push({x: -15, y: 10 - (20 * posOnSide / (pinsPerSide - 1))});
                            break;
                    }
                }
                break;

            default:
                // Default: two pins on sides
                pins.push({x: -20, y: 0});
                if (pinCount > 1) pins.push({x: 20, y: 0});
        }

        return pins;
    }

    // Connectivity and Wiring Methods
    drawConnection(connection) {
        const ctx = this.ctx;
        ctx.save();

        ctx.strokeStyle = connection.color || '#333';
        ctx.lineWidth = connection.width || 2;

        if (connection.type === 'wire') {
            this.drawWire(ctx, connection);
        } else if (connection.type === 'bus') {
            this.drawBus(ctx, connection);
        }

        ctx.restore();
    }

    drawWire(ctx, connection) {
        ctx.beginPath();
        ctx.moveTo(connection.startX, connection.startY);

        if (connection.waypoints && connection.waypoints.length > 0) {
            connection.waypoints.forEach(point => {
                ctx.lineTo(point.x, point.y);
            });
        }

        ctx.lineTo(connection.endX, connection.endY);
        ctx.stroke();

        // Draw connection points
        ctx.fillStyle = '#333';
        ctx.beginPath();
        ctx.arc(connection.startX, connection.startY, 3, 0, 2 * Math.PI);
        ctx.fill();

        ctx.beginPath();
        ctx.arc(connection.endX, connection.endY, 3, 0, 2 * Math.PI);
        ctx.fill();
    }

    drawBus(ctx, connection) {
        const busWidth = 4;
        ctx.lineWidth = busWidth;
        ctx.strokeStyle = '#1976D2';

        ctx.beginPath();
        ctx.moveTo(connection.startX, connection.startY);
        ctx.lineTo(connection.endX, connection.endY);
        ctx.stroke();

        // Draw bus indicators
        const midX = (connection.startX + connection.endX) / 2;
        const midY = (connection.startY + connection.endY) / 2;

        ctx.strokeStyle = '#333';
        ctx.lineWidth = 1;
        for (let i = -2; i <= 2; i++) {
            ctx.beginPath();
            ctx.moveTo(midX + i * 3, midY - 5);
            ctx.lineTo(midX + i * 3, midY + 5);
            ctx.stroke();
        }
    }

    // Event Handlers
    handleMouseDown(e) {
        const rect = this.canvas.getBoundingClientRect();
        const x = (e.clientX - rect.left) / this.zoom;
        const y = (e.clientY - rect.top) / this.zoom;

        this.mouseDown = true;
        this.lastMouseX = x;
        this.lastMouseY = y;

        switch (this.currentTool) {
            case 'select':
                this.handleSelectTool(x, y);
                break;
            case 'wire':
                this.handleWireTool(x, y);
                break;
            case 'move':
                this.handleMoveTool(x, y);
                break;
            case 'rotate':
                this.handleRotateTool(x, y);
                break;
            case 'delete':
                this.handleDeleteTool(x, y);
                break;
        }
    }

    handleMouseMove(e) {
        const rect = this.canvas.getBoundingClientRect();
        const x = (e.clientX - rect.left) / this.zoom;
        const y = (e.clientY - rect.top) / this.zoom;

        // Update cursor position display
        this.updateCursorPosition(x, y);

        if (this.mouseDown) {
            switch (this.currentTool) {
                case 'move':
                    this.handleMoveToolDrag(x, y);
                    break;
                case 'wire':
                    this.handleWireToolDrag(x, y);
                    break;
            }
        }

        this.lastMouseX = x;
        this.lastMouseY = y;
    }

    handleMouseUp(e) {
        this.mouseDown = false;

        if (this.currentTool === 'wire' && this.isDrawing) {
            this.finishWire();
        }
    }

    handleWheel(e) {
        e.preventDefault();

        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        const delta = e.deltaY > 0 ? 0.9 : 1.1;
        this.zoom *= delta;
        this.zoom = Math.max(0.1, Math.min(5.0, this.zoom));

        this.updateZoomDisplay();
        this.redraw();
    }

    handleDrop(e) {
        e.preventDefault();

        const componentId = e.dataTransfer.getData('text/plain');
        const rect = this.canvas.getBoundingClientRect();
        const x = (e.clientX - rect.left) / this.zoom;
        const y = (e.clientY - rect.top) / this.zoom;

        this.addComponentToCanvas(componentId, x, y);
    }

    handleKeyDown(e) {
        switch (e.key) {
            case 'Delete':
                this.deleteSelectedComponent();
                break;
            case 'Escape':
                this.clearSelection();
                break;
            case 'r':
            case 'R':
                if (this.selectedComponent) {
                    this.rotateComponent(this.selectedComponent);
                }
                break;
            case 'g':
            case 'G':
                this.toggleGrid();
                break;
            case 'z':
            case 'Z':
                if (e.ctrlKey) {
                    this.undo();
                }
                break;
            case 'y':
            case 'Y':
                if (e.ctrlKey) {
                    this.redo();
                }
                break;
        }
    }

    handleResize() {
        // Adjust canvas size if needed
        this.redraw();
    }

    // Tool Handlers
    handleSelectTool(x, y) {
        const component = this.getComponentAt(x, y);
        if (component) {
            this.selectComponent(component);
        } else {
            this.clearSelection();
        }
    }

    handleWireTool(x, y) {
        if (!this.isDrawing) {
            // Start new wire
            this.startWire(x, y);
        } else {
            // Add waypoint or finish wire
            this.addWireWaypoint(x, y);
        }
    }

    handleMoveTool(x, y) {
        const component = this.getComponentAt(x, y);
        if (component) {
            this.selectedComponent = component;
            this.dragOffset = {
                x: x - component.x,
                y: y - component.y
            };
        }
    }

    handleMoveToolDrag(x, y) {
        if (this.selectedComponent && this.dragOffset) {
            this.selectedComponent.x = x - this.dragOffset.x;
            this.selectedComponent.y = y - this.dragOffset.y;

            // Snap to grid
            if (this.showGrid) {
                this.selectedComponent.x = Math.round(this.selectedComponent.x / this.gridSize) * this.gridSize;
                this.selectedComponent.y = Math.round(this.selectedComponent.y / this.gridSize) * this.gridSize;
            }

            this.redraw();
            this.updatePropertiesPanel();
        }
    }

    handleRotateTool(x, y) {
        const component = this.getComponentAt(x, y);
        if (component) {
            this.rotateComponent(component);
        }
    }

    handleDeleteTool(x, y) {
        const component = this.getComponentAt(x, y);
        if (component) {
            this.deleteComponent(component);
        }
    }

    // Wire Drawing Methods
    startWire(x, y) {
        this.currentWire = {
            id: 'wire_' + Date.now(),
            type: 'wire',
            startX: x,
            startY: y,
            endX: x,
            endY: y,
            waypoints: [],
            color: '#333',
            width: 2
        };

        this.isDrawing = true;
        this.redraw();
    }

    addWireWaypoint(x, y) {
        if (this.currentWire) {
            this.currentWire.waypoints.push({x: this.currentWire.endX, y: this.currentWire.endY});
            this.currentWire.endX = x;
            this.currentWire.endY = y;
            this.redraw();
        }
    }

    handleWireToolDrag(x, y) {
        if (this.currentWire) {
            this.currentWire.endX = x;
            this.currentWire.endY = y;
            this.redraw();
        }
    }

    finishWire() {
        if (this.currentWire) {
            this.connections.set(this.currentWire.id, this.currentWire);
            this.currentWire = null;
            this.isDrawing = false;
            this.updateConnectionCount();
            this.redraw();
        }
    }

    // Simulation Engine
    initializeSimulation() {
        this.simulationEngine = {
            running: false,
            timeStep: 0.001, // 1ms
            currentTime: 0,
            maxTime: 10, // 10 seconds
            nodes: new Map(),
            components: new Map(),
            results: new Map()
        };
    }

    runSimulation() {
        if (this.simulationRunning) {
            this.stopSimulation();
            return;
        }

        try {
            this.simulationRunning = true;
            this.updateSimulationStatus('Running');

            // Build circuit netlist
            const netlist = this.buildNetlist();

            // Run SPICE-like simulation
            this.performCircuitAnalysis(netlist);

            // Display results
            this.displaySimulationResults();

            console.log('✅ Simulation completed successfully');
        } catch (error) {
            console.error('❌ Simulation failed:', error);
            this.updateSimulationStatus('Error');
        }
    }

    stopSimulation() {
        this.simulationRunning = false;
        this.updateSimulationStatus('Stopped');
    }

    buildNetlist() {
        const netlist = {
            components: [],
            nodes: new Set(),
            connections: []
        };

        // Add components to netlist
        this.components.forEach(component => {
            const netComponent = {
                id: component.id,
                type: component.type,
                value: component.value || component.specs,
                nodes: this.getComponentNodes(component)
            };
            netlist.components.push(netComponent);

            // Add nodes
            netComponent.nodes.forEach(node => {
                netlist.nodes.add(node);
            });
        });

        // Add connections
        this.connections.forEach(connection => {
            netlist.connections.push({
                id: connection.id,
                startNode: this.getNodeAt(connection.startX, connection.startY),
                endNode: this.getNodeAt(connection.endX, connection.endY)
            });
        });

        return netlist;
    }

    performCircuitAnalysis(netlist) {
        // Simplified circuit analysis for ECG amplifier
        const results = new Map();

        // DC Analysis
        this.performDCAnalysis(netlist, results);

        // AC Analysis
        this.performACAnalysis(netlist, results);

        // Transient Analysis
        this.performTransientAnalysis(netlist, results);

        this.simulationEngine.results = results;
    }

    performDCAnalysis(netlist, results) {
        // Simplified DC analysis
        const dcResults = {
            nodeVoltages: new Map(),
            componentCurrents: new Map(),
            powerDissipation: new Map()
        };

        // Calculate node voltages (simplified)
        netlist.nodes.forEach(node => {
            if (node === 'GND') {
                dcResults.nodeVoltages.set(node, 0);
            } else if (node.includes('VCC') || node.includes('VDD')) {
                dcResults.nodeVoltages.set(node, 5.0);
            } else if (node.includes('VEE') || node.includes('VSS')) {
                dcResults.nodeVoltages.set(node, -5.0);
            } else {
                // Estimate based on circuit topology
                dcResults.nodeVoltages.set(node, Math.random() * 0.1 - 0.05); // ±50mV
            }
        });

        results.set('DC', dcResults);
    }

    performACAnalysis(netlist, results) {
        // Simplified AC analysis for frequency response
        const frequencies = [];
        const magnitude = [];
        const phase = [];

        for (let f = 0.1; f <= 1000; f *= 1.1) {
            frequencies.push(f);

            // Calculate gain based on circuit components
            let gain = this.calculateGainAtFrequency(netlist, f);
            magnitude.push(20 * Math.log10(Math.abs(gain)));
            phase.push(Math.atan2(gain.imag || 0, gain.real || gain) * 180 / Math.PI);
        }

        results.set('AC', {
            frequencies,
            magnitude,
            phase
        });
    }

    performTransientAnalysis(netlist, results) {
        // Simplified transient analysis
        const timePoints = [];
        const outputVoltage = [];
        const inputVoltage = [];

        const dt = this.simulationEngine.timeStep;
        const maxTime = this.simulationEngine.maxTime;

        for (let t = 0; t <= maxTime; t += dt) {
            timePoints.push(t);

            // Generate ECG-like input signal
            const ecgInput = this.generateECGSignal(t);
            inputVoltage.push(ecgInput);

            // Calculate output based on circuit gain
            const gain = this.getCircuitGain(netlist);
            outputVoltage.push(ecgInput * gain);
        }

        results.set('Transient', {
            time: timePoints,
            input: inputVoltage,
            output: outputVoltage
        });
    }

    calculateGainAtFrequency(netlist, frequency) {
        // Find amplifier components and calculate gain
        let totalGain = 1;

        netlist.components.forEach(component => {
            if (component.type === 'INA128UA') {
                // Instrumentation amplifier gain
                const rg = 47; // Gain resistor in ohms
                const gain = 1 + (50000 / rg); // INA128 gain formula
                totalGain *= gain;
            } else if (component.type.includes('OPA') || component.type.includes('LM')) {
                // Op-amp gain (assume unity gain buffer)
                totalGain *= 1;
            }
        });

        // Apply frequency-dependent effects
        const hpf_fc = 0.5; // High-pass cutoff
        const lpf_fc = 150; // Low-pass cutoff

        // High-pass response
        const hpf_response = frequency / Math.sqrt(Math.pow(hpf_fc, 2) + Math.pow(frequency, 2));

        // Low-pass response
        const lpf_response = 1 / Math.sqrt(1 + Math.pow(frequency / lpf_fc, 2));

        return totalGain * hpf_response * lpf_response;
    }

    generateECGSignal(time) {
        // Generate realistic ECG signal
        const heartRate = 72; // BPM
        const period = 60 / heartRate; // seconds
        const t = time % period;
        const normalizedTime = t / period;

        // ECG waveform components
        let ecg = 0;

        // P wave (0.08-0.12s)
        if (normalizedTime >= 0.1 && normalizedTime <= 0.2) {
            const pTime = (normalizedTime - 0.1) / 0.1;
            ecg += 0.1 * Math.sin(Math.PI * pTime);
        }

        // QRS complex (0.06-0.10s)
        if (normalizedTime >= 0.3 && normalizedTime <= 0.4) {
            const qrsTime = (normalizedTime - 0.3) / 0.1;
            if (qrsTime < 0.3) {
                ecg -= 0.2 * Math.sin(Math.PI * qrsTime / 0.3); // Q wave
            } else if (qrsTime < 0.7) {
                ecg += 1.0 * Math.sin(Math.PI * (qrsTime - 0.3) / 0.4); // R wave
            } else {
                ecg -= 0.3 * Math.sin(Math.PI * (qrsTime - 0.7) / 0.3); // S wave
            }
        }

        // T wave (0.16s)
        if (normalizedTime >= 0.6 && normalizedTime <= 0.8) {
            const tTime = (normalizedTime - 0.6) / 0.2;
            ecg += 0.3 * Math.sin(Math.PI * tTime);
        }

        // Add noise
        ecg += (Math.random() - 0.5) * 0.01;

        return ecg * 0.001; // Convert to mV
    }

    getCircuitGain(netlist) {
        let gain = 1;

        netlist.components.forEach(component => {
            if (component.type === 'INA128UA') {
                gain *= 1100; // Typical ECG amplifier gain
            }
        });

        return gain;
    }

    displaySimulationResults() {
        const results = this.simulationEngine.results;

        // Display in measurement tools
        this.updateOscilloscope(results.get('Transient'));
        this.updateFrequencyAnalysis(results.get('AC'));
        this.updateMultimeter(results.get('DC'));

        // Show results notification
        if (typeof showNotification === 'function') {
            showNotification('Simulation completed successfully! Check measurement tools for results.', 'success');
        }
    }

    updateOscilloscope(transientData) {
        if (!transientData) return;

        const oscilloscope = this.measurementTools.get('oscilloscope');
        oscilloscope.data.set('CH1', {
            time: transientData.time,
            voltage: transientData.input,
            label: 'Input Signal'
        });

        oscilloscope.data.set('CH2', {
            time: transientData.time,
            voltage: transientData.output,
            label: 'Output Signal'
        });

        oscilloscope.active = true;
        this.displayOscilloscopeData();
    }

    updateFrequencyAnalysis(acData) {
        if (!acData || typeof Plotly === 'undefined') return;

        const trace = {
            x: acData.frequencies,
            y: acData.magnitude,
            type: 'scatter',
            mode: 'lines',
            name: 'Frequency Response',
            line: { color: '#4CAF50', width: 2 }
        };

        const layout = {
            title: 'Circuit Frequency Response',
            xaxis: {
                title: 'Frequency (Hz)',
                type: 'log'
            },
            yaxis: {
                title: 'Magnitude (dB)'
            },
            margin: { t: 50, r: 50, b: 50, l: 60 }
        };

        // Create or update frequency response plot
        const plotDiv = document.getElementById('frequencyPlot') || this.createFrequencyPlotDiv();
        Plotly.newPlot(plotDiv, [trace], layout, {responsive: true});
    }

    updateMultimeter(dcData) {
        if (!dcData) return;

        const multimeter = this.measurementTools.get('multimeter');

        // Calculate RMS output voltage
        const outputNode = 'OUT';
        const outputVoltage = dcData.nodeVoltages.get(outputNode) || 0;

        multimeter.value = Math.abs(outputVoltage);
        multimeter.unit = 'V';
        multimeter.active = true;

        this.updateMultimeterDisplay();
    }

    // Utility Methods
    addComponentToCanvas(componentId, x = 100, y = 100) {
        const componentDef = this.findComponentDefinition(componentId);
        if (!componentDef) return;

        const component = {
            id: componentId + '_' + Date.now(),
            type: componentId,
            name: componentDef.name,
            x: x,
            y: y,
            rotation: 0,
            symbol: componentDef.symbol,
            color: componentDef.color,
            pins: componentDef.pins,
            pinout: componentDef.pinout,
            specs: componentDef.specs,
            value: componentDef.specs.value || '1k'
        };

        this.components.set(component.id, component);
        this.updateComponentCount();
        this.redraw();

        return component;
    }

    findComponentDefinition(componentId) {
        for (const category in this.componentLibrary) {
            const component = this.componentLibrary[category].find(c => c.id === componentId);
            if (component) return component;
        }
        return null;
    }

    getComponentAt(x, y) {
        for (const [id, component] of this.components) {
            const dx = x - component.x;
            const dy = y - component.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance < 30) { // Hit test radius
                return component;
            }
        }
        return null;
    }

    selectComponent(component) {
        this.selectedComponent = component;
        this.updatePropertiesPanel();
        this.redraw();
    }

    clearSelection() {
        this.selectedComponent = null;
        this.updatePropertiesPanel();
        this.redraw();
    }

    deleteComponent(component) {
        this.components.delete(component.id);
        if (this.selectedComponent === component) {
            this.selectedComponent = null;
        }
        this.updateComponentCount();
        this.updatePropertiesPanel();
        this.redraw();
    }

    rotateComponent(component) {
        component.rotation = (component.rotation + 90) % 360;
        this.updatePropertiesPanel();
        this.redraw();
    }

    redraw() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw grid
        this.drawGrid();

        // Draw connections
        this.connections.forEach(connection => {
            this.drawConnection(connection);
        });

        // Draw components
        this.components.forEach(component => {
            this.drawComponent(component);

            // Highlight selected component
            if (component === this.selectedComponent) {
                this.highlightComponent(component);
            }
        });

        // Draw current wire being drawn
        if (this.currentWire) {
            this.drawConnection(this.currentWire);
        }
    }

    highlightComponent(component) {
        this.ctx.save();
        this.ctx.strokeStyle = '#FF4081';
        this.ctx.lineWidth = 3;
        this.ctx.setLineDash([5, 5]);

        this.ctx.strokeRect(
            component.x - 35,
            component.y - 35,
            70,
            70
        );

        this.ctx.restore();
    }

    updatePropertiesPanel() {
        const nameInput = document.getElementById('componentName');
        const valueInput = document.getElementById('componentValue');
        const packageSelect = document.getElementById('componentPackage');
        const positionXInput = document.getElementById('positionX');
        const positionYInput = document.getElementById('positionY');
        const rotationSelect = document.getElementById('rotation');

        if (this.selectedComponent) {
            if (nameInput) nameInput.value = this.selectedComponent.name || '';
            if (valueInput) valueInput.value = this.selectedComponent.value || '';
            if (packageSelect) packageSelect.value = this.selectedComponent.package || '';
            if (positionXInput) positionXInput.value = this.selectedComponent.x || 0;
            if (positionYInput) positionYInput.value = this.selectedComponent.y || 0;
            if (rotationSelect) rotationSelect.value = this.selectedComponent.rotation || 0;
        } else {
            if (nameInput) nameInput.value = '';
            if (valueInput) valueInput.value = '';
            if (packageSelect) packageSelect.value = '';
            if (positionXInput) positionXInput.value = '';
            if (positionYInput) positionYInput.value = '';
            if (rotationSelect) rotationSelect.value = '0';
        }
    }

    updateCursorPosition(x, y) {
        const cursorPos = document.getElementById('cursorPosition');
        if (cursorPos) {
            cursorPos.textContent = `X: ${Math.round(x)}, Y: ${Math.round(y)}`;
        }
    }

    updateComponentCount() {
        const componentCount = document.getElementById('componentCount');
        if (componentCount) {
            componentCount.textContent = `Components: ${this.components.size}`;
        }
    }

    updateConnectionCount() {
        const connectionCount = document.getElementById('connectionCount');
        if (connectionCount) {
            connectionCount.textContent = `Connections: ${this.connections.size}`;
        }
    }

    updateZoomDisplay() {
        const zoomLevel = document.getElementById('zoomLevel');
        if (zoomLevel) {
            zoomLevel.textContent = `${Math.round(this.zoom * 100)}%`;
        }
    }

    updateSimulationStatus(status) {
        const statusElement = document.getElementById('simulationStatus');
        const indicator = document.getElementById('simulationIndicator');

        if (statusElement) {
            statusElement.textContent = status;
        }

        if (indicator) {
            indicator.className = 'fas fa-circle';
            switch (status) {
                case 'Running':
                    indicator.style.color = '#4CAF50';
                    break;
                case 'Stopped':
                    indicator.style.color = '#F44336';
                    break;
                case 'Error':
                    indicator.style.color = '#FF9800';
                    break;
                default:
                    indicator.style.color = '#9E9E9E';
            }
        }
    }

    // Measurement Tool Display Methods
    displayOscilloscopeData() {
        const oscilloscope = this.measurementTools.get('oscilloscope');
        if (!oscilloscope.active || typeof Plotly === 'undefined') return;

        const traces = [];

        oscilloscope.data.forEach((channel, name) => {
            traces.push({
                x: channel.time,
                y: channel.voltage,
                type: 'scatter',
                mode: 'lines',
                name: channel.label,
                line: { width: 2 }
            });
        });

        const layout = {
            title: 'Oscilloscope - ECG Signal Analysis',
            xaxis: { title: 'Time (s)' },
            yaxis: { title: 'Voltage (V)' },
            margin: { t: 50, r: 50, b: 50, l: 60 }
        };

        const plotDiv = document.getElementById('oscilloscopePlot') || this.createOscilloscopePlotDiv();
        Plotly.newPlot(plotDiv, traces, layout, {responsive: true});
    }

    updateMultimeterDisplay() {
        const multimeter = this.measurementTools.get('multimeter');
        if (!multimeter.active) return;

        // Update virtual multimeter display
        const displays = document.querySelectorAll('.multimeter-display');
        displays.forEach(display => {
            display.textContent = `${multimeter.value.toFixed(3)}${multimeter.unit}`;
        });
    }

    createOscilloscopePlotDiv() {
        const plotDiv = document.createElement('div');
        plotDiv.id = 'oscilloscopePlot';
        plotDiv.style.cssText = `
            width: 100%;
            height: 300px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
        `;

        // Add to simulation tab or create modal
        const simulationTab = document.querySelector('[data-tab="simulation"]');
        if (simulationTab) {
            simulationTab.appendChild(plotDiv);
        }

        return plotDiv;
    }

    createFrequencyPlotDiv() {
        const plotDiv = document.createElement('div');
        plotDiv.id = 'frequencyPlot';
        plotDiv.style.cssText = `
            width: 100%;
            height: 300px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
        `;

        const simulationTab = document.querySelector('[data-tab="simulation"]');
        if (simulationTab) {
            simulationTab.appendChild(plotDiv);
        }

        return plotDiv;
    }

    // Helper methods for simulation
    getComponentNodes(component) {
        const nodes = [];
        const pinPositions = this.getComponentPinPositions(component);

        pinPositions.forEach((pin, index) => {
            const globalX = component.x + pin.x;
            const globalY = component.y + pin.y;
            nodes.push(`${component.id}_pin${index + 1}`);
        });

        return nodes;
    }

    getNodeAt(x, y) {
        // Find the nearest component pin
        let nearestNode = null;
        let minDistance = Infinity;

        this.components.forEach(component => {
            const pinPositions = this.getComponentPinPositions(component);
            pinPositions.forEach((pin, index) => {
                const globalX = component.x + pin.x;
                const globalY = component.y + pin.y;
                const distance = Math.sqrt(Math.pow(x - globalX, 2) + Math.pow(y - globalY, 2));

                if (distance < minDistance && distance < 10) {
                    minDistance = distance;
                    nearestNode = `${component.id}_pin${index + 1}`;
                }
            });
        });

        return nearestNode || `node_${Math.round(x)}_${Math.round(y)}`;
    }
}

// Global Enhanced Workbench instance
let enhancedWorkbench;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('canvasContent')) {
        enhancedWorkbench = new EnhancedWorkbench();
        window.enhancedWorkbench = enhancedWorkbench;
    }
});

// Global functions for HTML onclick handlers
function showCategory(category) {
    if (enhancedWorkbench) {
        enhancedWorkbench.populateComponentList(category);

        // Update active category
        document.querySelectorAll('.category').forEach(cat => {
            cat.classList.remove('active');
        });
        event.target.classList.add('active');
    }
}

function addComponentToCanvas(componentId, x, y) {
    if (enhancedWorkbench) {
        enhancedWorkbench.addComponentToCanvas(componentId, x, y);
    }
}

function selectTool(tool) {
    if (enhancedWorkbench) {
        enhancedWorkbench.currentTool = tool;

        // Update tool button states
        document.querySelectorAll('.toolbar-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        event.target.classList.add('active');
    }
}

function runSimulation() {
    if (enhancedWorkbench) {
        enhancedWorkbench.runSimulation();
    }
}

function stopSimulation() {
    if (enhancedWorkbench) {
        enhancedWorkbench.stopSimulation();
    }
}

function toggleGrid() {
    if (enhancedWorkbench) {
        enhancedWorkbench.showGrid = !enhancedWorkbench.showGrid;
        enhancedWorkbench.redraw();
    }
}

function zoomIn() {
    if (enhancedWorkbench) {
        enhancedWorkbench.zoom *= 1.2;
        enhancedWorkbench.updateZoomDisplay();
        enhancedWorkbench.redraw();
    }
}

function zoomOut() {
    if (enhancedWorkbench) {
        enhancedWorkbench.zoom /= 1.2;
        enhancedWorkbench.updateZoomDisplay();
        enhancedWorkbench.redraw();
    }
}

function fitToScreen() {
    if (enhancedWorkbench) {
        enhancedWorkbench.zoom = 1.0;
        enhancedWorkbench.updateZoomDisplay();
        enhancedWorkbench.redraw();
    }
}

function clearCanvas() {
    if (enhancedWorkbench) {
        enhancedWorkbench.components.clear();
        enhancedWorkbench.connections.clear();
        enhancedWorkbench.selectedComponent = null;
        enhancedWorkbench.updateComponentCount();
        enhancedWorkbench.updateConnectionCount();
        enhancedWorkbench.updatePropertiesPanel();
        enhancedWorkbench.redraw();
    }
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedWorkbench;
}
