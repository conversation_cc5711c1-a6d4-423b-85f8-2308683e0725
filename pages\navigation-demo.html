<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Demo - ECG Signal System</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/component-pages.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .demo-container {
            padding-top: 70px;
            min-height: 100vh;
            background: #f8f9fa;
        }

        .demo-content {
            padding: 4rem 0;
        }

        .demo-section {
            background: white;
            margin: 2rem 0;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .demo-section h3 {
            color: #667eea;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .demo-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-top: 1rem;
        }

        .demo-btn {
            padding: 10px 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .demo-btn:hover {
            background: #764ba2;
            transform: translateY(-2px);
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .feature-list i {
            color: #4CAF50;
            width: 20px;
        }

        .demo-intro {
            text-align: center;
            color: #666;
            margin-bottom: 3rem;
        }

        .api-output {
            margin-top: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 6px;
            font-family: monospace;
            display: none;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-heartbeat"></i>
                <span>ECG Simulator</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="../index.html" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="circuit-design.html" class="nav-link">Circuit Design</a>
                </li>
                <li class="nav-item">
                    <a href="signal-analysis.html" class="nav-link">Signal Analysis</a>
                </li>
                <li class="nav-item">
                    <a href="#demo" class="nav-link active">Navigation Demo</a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Enhanced Breadcrumb -->
    <nav class="enhanced-breadcrumb">
        <div class="container">
            <div class="breadcrumb-container">
                <div class="breadcrumb">
                    <a href="../index.html" class="breadcrumb-link">
                        <i class="fas fa-home"></i> Home
                    </a>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-current">Navigation Demo</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Demo Content -->
    <div class="demo-container">
        <div class="container">
            <div class="demo-content">
                <h1 class="section-title">Navigation System Demo</h1>
                <p class="demo-intro">
                    Explore the comprehensive navigation features added to the ECG Signal System
                </p>

                <!-- Return Navigation Button Demo -->
                <div class="demo-section">
                    <h3><i class="fas fa-arrow-left"></i> Return Navigation Button</h3>
                    <p>A floating return button appears on the left side of the screen that allows you to:</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Go back to the previous page with a single click</li>
                        <li><i class="fas fa-check"></i> Right-click to open navigation history panel</li>
                        <li><i class="fas fa-check"></i> Smart path resolution based on current page location</li>
                        <li><i class="fas fa-check"></i> Responsive design that adapts to mobile devices</li>
                    </ul>
                    <div class="demo-buttons">
                        <button type="button" class="demo-btn" onclick="navigationManager.goBack()">
                            <i class="fas fa-arrow-left"></i> Test Go Back
                        </button>
                        <button type="button" class="demo-btn" onclick="navigationManager.toggleHistoryPanel()">
                            <i class="fas fa-history"></i> Show History Panel
                        </button>
                    </div>
                </div>

                <!-- Navigation History Demo -->
                <div class="demo-section">
                    <h3><i class="fas fa-history"></i> Navigation History</h3>
                    <p>The system automatically tracks your navigation history and provides:</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Automatic page tracking with timestamps</li>
                        <li><i class="fas fa-check"></i> Visual history panel with page icons and titles</li>
                        <li><i class="fas fa-check"></i> Local storage persistence across sessions</li>
                        <li><i class="fas fa-check"></i> Maximum history limit to prevent memory issues</li>
                    </ul>
                    <div class="demo-buttons">
                        <button type="button" class="demo-btn" onclick="showHistoryInfo()">
                            <i class="fas fa-info"></i> Show Current History
                        </button>
                        <button type="button" class="demo-btn" onclick="navigationManager.clearHistory()">
                            <i class="fas fa-trash"></i> Clear History
                        </button>
                    </div>
                </div>

                <!-- Quick Navigation Menu Demo -->
                <div class="demo-section">
                    <h3><i class="fas fa-compass"></i> Quick Navigation Menu</h3>
                    <p>A floating quick navigation menu in the bottom-right corner provides:</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Quick access to main sections of the application</li>
                        <li><i class="fas fa-check"></i> Expandable circular menu with smooth animations</li>
                        <li><i class="fas fa-check"></i> Tooltips showing destination page names</li>
                        <li><i class="fas fa-check"></i> Auto-close when clicking outside the menu</li>
                    </ul>
                    <p><strong>Look for the compass icon in the bottom-right corner!</strong></p>
                </div>

                <!-- Enhanced Breadcrumbs Demo -->
                <div class="demo-section">
                    <h3><i class="fas fa-map-signs"></i> Enhanced Breadcrumbs</h3>
                    <p>The breadcrumb navigation has been enhanced with:</p>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Sticky positioning for always-visible navigation</li>
                        <li><i class="fas fa-check"></i> Action buttons for common navigation tasks</li>
                        <li><i class="fas fa-check"></i> Responsive design that stacks on mobile devices</li>
                        <li><i class="fas fa-check"></i> Visual separation from main content</li>
                    </ul>
                    <p><strong>See the enhanced breadcrumb bar above this content!</strong></p>
                </div>

                <!-- Test Navigation Links -->
                <div class="demo-section">
                    <h3><i class="fas fa-external-link-alt"></i> Test Navigation</h3>
                    <p>Use these links to test the navigation system across different pages:</p>
                    <div class="demo-buttons">
                        <a href="../index.html" class="demo-btn">
                            <i class="fas fa-home"></i> Home Page
                        </a>
                        <a href="circuit-design.html" class="demo-btn">
                            <i class="fas fa-microchip"></i> Circuit Design
                        </a>
                        <a href="ecg-electrodes.html" class="demo-btn">
                            <i class="fas fa-user"></i> ECG Electrodes
                        </a>
                        <a href="virtual-workbench.html" class="demo-btn">
                            <i class="fas fa-tools"></i> Virtual Workbench
                        </a>
                        <a href="interactive-diagrams.html" class="demo-btn">
                            <i class="fas fa-sitemap"></i> Interactive Diagrams
                        </a>
                    </div>
                </div>

                <!-- API Demo -->
                <div class="demo-section">
                    <h3><i class="fas fa-code"></i> Navigation API</h3>
                    <p>Developers can use the navigation API programmatically:</p>
                    <div class="demo-buttons">
                        <button type="button" class="demo-btn" onclick="testNavigationAPI()">
                            <i class="fas fa-play"></i> Test API Functions
                        </button>
                        <button type="button" class="demo-btn" onclick="showNavigationInfo()">
                            <i class="fas fa-info-circle"></i> Show Navigation Info
                        </button>
                    </div>
                    <div id="api-output" class="api-output"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/error-handler.js"></script>
    <script src="../js/navigation-utils.js"></script>
    <script src="../js/main.js"></script>
    <script>
        function showHistoryInfo() {
            if (window.navigationManager) {
                const history = navigationManager.history;
                alert(`Current navigation history has ${history.length} items:\n\n` +
                      history.slice(0, 5).map((item, index) =>
                          `${index + 1}. ${item.title} (${item.path})`
                      ).join('\n') +
                      (history.length > 5 ? '\n... and more' : ''));
            } else {
                alert('Navigation manager not available');
            }
        }

        function testNavigationAPI() {
            const output = document.getElementById('api-output');
            output.style.display = 'block';

            if (window.navigationManager) {
                const info = {
                    currentPage: navigationManager.currentPage,
                    historyLength: navigationManager.history.length,
                    availablePages: Object.keys(navigationManager.pages).length,
                    isInitialized: navigationManager.isInitialized
                };

                output.innerHTML = `
                    <strong>Navigation Manager Status:</strong><br>
                    Current Page: ${info.currentPage}<br>
                    History Length: ${info.historyLength}<br>
                    Available Pages: ${info.availablePages}<br>
                    Initialized: ${info.isInitialized}<br>
                    <br>
                    <strong>Available API Methods:</strong><br>
                    - navigationManager.goBack()<br>
                    - navigationManager.navigateToPage(path)<br>
                    - navigationManager.toggleHistoryPanel()<br>
                    - navigationManager.clearHistory()<br>
                    - navigationManager.getPageInfo(path)
                `;
            } else {
                output.innerHTML = '<strong>Error:</strong> Navigation manager not available';
            }
        }

        function showNavigationInfo() {
            if (window.navigationManager) {
                const currentPageInfo = navigationManager.getPageInfo(navigationManager.currentPage);
                alert(`Current Page Information:\n\n` +
                      `Title: ${currentPageInfo ? currentPageInfo.title : 'Unknown'}\n` +
                      `Icon: ${currentPageInfo ? currentPageInfo.icon : 'Unknown'}\n` +
                      `Category: ${currentPageInfo ? currentPageInfo.category : 'Unknown'}\n` +
                      `Path: ${navigationManager.currentPage}`);
            }
        }
    </script>
</body>
</html>
