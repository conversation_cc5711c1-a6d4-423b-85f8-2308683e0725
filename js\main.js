// Main JavaScript for ECG Signal System Virtual Simulation

// Global variables
let currentSection = 'home';
let ecgSimulator = null;
let componentModal = null;

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Initialize application
function initializeApp() {
    setupNavigation();
    setupModal();
    initializeECGPreview();
    setupSystemBlocks();
    setupResponsiveMenu();

    console.log('ECG Signal System Virtual Simulation initialized');
}

// Navigation setup
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Remove active class from all links
            navLinks.forEach(l => l.classList.remove('active'));

            // Add active class to clicked link
            this.classList.add('active');

            // Get target section
            const target = this.getAttribute('href').substring(1);
            navigateToSection(target);
        });
    });
}

// Navigate to specific section
function navigateToSection(section) {
    currentSection = section;

    // Smooth scroll to section
    const targetElement = document.getElementById(section);
    if (targetElement) {
        targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }

    // Handle section-specific logic
    switch(section) {
        case 'circuit':
            loadCircuitSimulator();
            break;
        case 'signals':
            loadSignalAnalyzer();
            break;
        case 'pcb':
            loadPCBViewer();
            break;
        case 'about':
            loadAboutSection();
            break;
    }
}

// Modal setup
function setupModal() {
    componentModal = document.getElementById('componentModal');
    if (!componentModal) return;

    const closeBtn = document.querySelector('.close');
    if (closeBtn) {
        closeBtn.addEventListener('click', closeModal);
    }

    window.addEventListener('click', function(e) {
        if (e.target === componentModal) {
            closeModal();
        }
    });
}

// Open modal with component details
function openModal(componentType) {
    if (!componentModal) return;

    const modalBody = document.getElementById('modalBody');
    if (!modalBody) return;

    modalBody.innerHTML = getComponentDetails(componentType);
    componentModal.style.display = 'block';
}

// Close modal
function closeModal() {
    if (componentModal) {
        componentModal.style.display = 'none';
    }
}

// Get component details for modal
function getComponentDetails(componentType) {
    const components = {
        electrodes: {
            title: 'ECG Electrodes',
            description: 'Ag/AgCl electrodes for bioelectric signal acquisition',
            specs: [
                'Material: Silver/Silver Chloride',
                'Impedance: < 2kΩ at 10Hz',
                'Offset Voltage: < 100μV',
                'Noise: < 50μVpp'
            ]
        },
        amplifier: {
            title: 'INA128UA Instrumentation Amplifier',
            description: 'High-precision, low-noise instrumentation amplifier for ECG signal conditioning',
            specs: [
                'Gain Range: 1 to 10,000',
                'Input Offset Voltage: 50μV max',
                'CMRR: 120dB min',
                'Input Bias Current: 5nA max',
                'Bandwidth: 200kHz'
            ]
        },
        filter: {
            title: 'Analog Filter Stages',
            description: 'Multi-stage filtering for noise reduction and signal conditioning',
            specs: [
                'High-pass: 0.5Hz (-3dB)',
                'Low-pass: 150Hz (-3dB)',
                'Notch: 50/60Hz rejection',
                'Total Gain: 1100x'
            ]
        },
        adc: {
            title: 'STM32F4 ADC',
            description: '12-bit successive approximation ADC for digital conversion',
            specs: [
                'Resolution: 12-bit',
                'Sampling Rate: up to 2.4 MSPS',
                'Input Range: 0-3.3V',
                'DNL: ±1 LSB',
                'INL: ±2 LSB'
            ]
        },
        processing: {
            title: 'Digital Signal Processing',
            description: 'Real-time digital filtering and analysis algorithms',
            specs: [
                'Sampling Frequency: 256Hz',
                'Digital Filtering: IIR/FIR',
                'Heart Rate Detection',
                'Arrhythmia Analysis'
            ]
        }
    };

    const component = components[componentType];
    if (!component) return '<p>Component details not found.</p>';

    return `
        <h2>${component.title}</h2>
        <p>${component.description}</p>
        <h3>Specifications:</h3>
        <ul>
            ${component.specs.map(spec => `<li>${spec}</li>`).join('')}
        </ul>
    `;
}

// Setup system block interactions
function setupSystemBlocks() {
    const systemBlocks = document.querySelectorAll('.system-block');

    systemBlocks.forEach(block => {
        block.addEventListener('click', function() {
            const componentType = this.getAttribute('data-component');
            openModal(componentType);
        });
    });
}

// Initialize ECG preview in hero section
function initializeECGPreview() {
    const previewContainer = document.getElementById('ecg-preview');
    if (previewContainer && typeof generateECGPreview === 'function') {
        generateECGPreview(previewContainer);
    }
}

// Responsive menu setup
function setupResponsiveMenu() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');

    hamburger.addEventListener('click', function() {
        hamburger.classList.toggle('active');
        navMenu.classList.toggle('active');
    });

    // Close menu when clicking on a link
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', function() {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        });
    });
}

// Button click handlers
function startSimulation() {
    navigateToSection('signals');
    // Initialize full ECG simulation
    if (typeof initializeECGSimulation === 'function') {
        initializeECGSimulation();
    }
}

function viewComponents() {
    navigateToSection('circuit');
    // Load circuit component viewer
    if (typeof loadCircuitComponents === 'function') {
        loadCircuitComponents();
    }
}

// Load different sections
function loadCircuitSimulator() {
    console.log('Loading circuit simulator...');
    // This will be implemented in circuit-simulator.js
}

function loadSignalAnalyzer() {
    console.log('Loading signal analyzer...');
    // This will be implemented in ecg-simulator.js
}

function loadPCBViewer() {
    console.log('Loading PCB viewer...');
    // This will be implemented separately
}

function loadAboutSection() {
    console.log('Loading about section...');
    // Display technical documentation
}

// Enhanced notification system
function showNotification(message, type = 'info', title = null) {
    // Create notification container if it doesn't exist
    let container = document.querySelector('.notification-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'notification-container';
        document.body.appendChild(container);
    }

    // Determine icon and title based on type
    const icons = {
        'info': 'fas fa-info-circle',
        'success': 'fas fa-check-circle',
        'warning': 'fas fa-exclamation-triangle',
        'error': 'fas fa-times-circle'
    };

    const titles = {
        'info': title || 'Information',
        'success': title || 'Success',
        'warning': title || 'Warning',
        'error': title || 'Error'
    };

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-icon">
            <i class="${icons[type]}"></i>
        </div>
        <div class="notification-content">
            <div class="notification-title">${titles[type]}</div>
            <div class="notification-message">${message}</div>
        </div>
        <button type="button" class="notification-close" onclick="removeNotification(this.parentElement)">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Add to container
    container.appendChild(notification);

    // Show with animation
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);

    // Auto remove after 5 seconds
    setTimeout(() => {
        removeNotification(notification);
    }, 5000);

    return notification;
}

function removeNotification(notification) {
    if (notification && notification.parentElement) {
        notification.classList.remove('show');
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 300);
    }
}

// Error handling
window.addEventListener('error', function(e) {
    console.error('Application error:', e.error);
    showNotification('An error occurred. Please check the console for details.', 'error');
});

// Navigation functions for quick access cards
function navigateToPage(page) {
    // Page mapping with fallbacks for missing pages
    const pageMap = {
        'circuit': 'pages/circuit-design.html',
        'signals': 'pages/signal-analysis.html',
        'pcb': 'pages/pcb-viewer.html',
        'workbench': 'pages/virtual-workbench.html',
        'diagrams': 'pages/interactive-diagrams.html',
        'analysis': 'pages/data-analysis.html',
        'electrodes': 'pages/ecg-electrodes.html',
        'demo': 'pages/navigation-demo.html',
        // Fallbacks for missing pages
        'amplifier': 'pages/circuit-design.html#amplifier',
        'filters': 'pages/circuit-design.html#filters',
        'adc': 'pages/circuit-design.html#adc',
        'processing': 'pages/data-analysis.html#processing'
    };

    const targetPage = pageMap[page];

    if (targetPage) {
        // Use navigation manager if available for better tracking
        if (window.navigationManager) {
            window.navigationManager.navigateToPage(targetPage);
        } else {
            window.location.href = targetPage;
        }
    } else {
        console.warn('Unknown page:', page);
        // Show user-friendly message
        showNotification('Page not found. Redirecting to home.', 'warning');
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 2000);
    }
}

// Export functions for use in other modules
window.ECGApp = {
    navigateToSection,
    navigateToPage,
    openModal,
    closeModal,
    showNotification,
    startSimulation,
    viewComponents
};

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    try {
        setupModal();

        // Initialize ECG preview if container exists
        const previewContainer = document.getElementById('ecg-preview');
        if (previewContainer) {
            if (typeof ECGSimulator !== 'undefined') {
                const ecgSim = new ECGSimulator();
                ecgSim.generatePreview(previewContainer);
            } else {
                console.warn('ECGSimulator not available');
            }
        }

        // Setup system block interactions
        setupSystemBlocks();

        console.log('✅ Main application initialized successfully');

        // Show welcome notification
        setTimeout(() => {
            showNotification(
                'Welcome to the ECG Signal System! Use the navigation features to explore different sections.',
                'success',
                'Welcome!'
            );
        }, 1000);

    } catch (error) {
        console.error('Error initializing main application:', error);
        showNotification('Failed to initialize application. Please refresh the page.', 'error');
    }
});
