// Circuit Design and Simulation Functions
// Enhanced ECG Circuit Design Implementation

class CircuitDesigner {
    constructor() {
        this.currentSchematicView = 'complete';
        this.currentCalculator = 'gain';
        this.components = new Map();
        this.isInitialized = false;
        
        this.init();
    }
    
    init() {
        if (this.isInitialized) return;
        
        try {
            this.addSVGInteractivity();
            this.initializeControls();
            this.switchCalculator();
            this.updateSimulation();
            
            this.isInitialized = true;
            console.log('✅ Circuit Designer initialized');
        } catch (error) {
            console.error('❌ Circuit Designer initialization failed:', error);
            if (typeof showNotification === 'function') {
                showNotification('Circuit designer failed to initialize', 'error');
            }
        }
    }
    
    initializeControls() {
        // Initialize range sliders
        const gainSlider = document.getElementById('gain');
        const freqSlider = document.getElementById('frequency');
        
        if (gainSlider) {
            gainSlider.addEventListener('input', () => this.updateSimulation());
        }
        if (freqSlider) {
            freqSlider.addEventListener('input', () => this.updateSimulation());
        }
    }
    
    addSVGInteractivity() {
        const svgGroups = ['inputStage', 'inaStage', 'filterStage', 'outputStage', 'adcStage'];
        
        svgGroups.forEach(groupId => {
            const group = document.getElementById(groupId);
            if (group) {
                group.style.cursor = 'pointer';
                group.addEventListener('click', () => this.showComponentDetails(groupId));
                group.addEventListener('mouseenter', () => this.highlightComponent(groupId, true));
                group.addEventListener('mouseleave', () => this.highlightComponent(groupId, false));
            }
        });
    }
    
    highlightComponent(componentId, highlight) {
        const component = document.getElementById(componentId);
        if (component) {
            const rect = component.querySelector('rect');
            if (rect) {
                if (highlight) {
                    rect.style.filter = 'brightness(1.1) drop-shadow(0 0 5px rgba(102, 126, 234, 0.5))';
                } else {
                    rect.style.filter = 'none';
                }
            }
        }
    }
    
    showComponentDetails(componentId) {
        const details = {
            'inputStage': {
                title: 'Input Protection Stage',
                description: 'ESD protection and electrode interface',
                specs: [
                    'Input Impedance: >10MΩ',
                    'Common Mode Range: ±10V',
                    'ESD Protection: ±15kV',
                    'Electrode Type: Ag/AgCl'
                ]
            },
            'inaStage': {
                title: 'INA128UA Instrumentation Amplifier',
                description: 'High-precision differential amplification',
                specs: [
                    'Gain: 1100x (programmable)',
                    'CMRR: 120dB minimum',
                    'Input Offset: 50μV maximum',
                    'Bandwidth: 200kHz'
                ]
            },
            'filterStage': {
                title: 'Analog Filter Stage',
                description: 'Multi-stage filtering for signal conditioning',
                specs: [
                    'High-Pass: 0.5Hz (-3dB)',
                    'Low-Pass: 150Hz (-3dB)',
                    'Notch Filter: 50/60Hz',
                    'Op-Amp: OPA2131UA'
                ]
            },
            'outputStage': {
                title: 'Output Level Shifter',
                description: 'Level shifting for ADC interface',
                specs: [
                    'Input Range: ±5.5V',
                    'Output Range: 0-3.3V',
                    'DC Offset: 1.65V',
                    'Op-Amp: OP07D'
                ]
            },
            'adcStage': {
                title: 'STM32F4 ADC Interface',
                description: 'Analog-to-digital conversion',
                specs: [
                    'Resolution: 12-bit',
                    'Sample Rate: 256Hz',
                    'Reference: 3.3V',
                    'ENOB: 10.5 bits'
                ]
            }
        };

        const detail = details[componentId];
        if (detail && typeof showNotification === 'function') {
            const specsText = detail.specs.join('\n• ');
            showNotification(
                `${detail.description}\n\n• ${specsText}`,
                'info',
                detail.title
            );
        }
    }
    
    updateSimulation() {
        const inputSignal = document.getElementById('inputSignal')?.value || 'ecg';
        const gain = document.getElementById('gain')?.value || 1100;
        const frequency = document.getElementById('frequency')?.value || 1;

        // Update display values
        const gainValue = document.getElementById('gainValue');
        const frequencyValue = document.getElementById('frequencyValue');
        
        if (gainValue) gainValue.textContent = gain + 'x';
        if (frequencyValue) frequencyValue.textContent = frequency + ' Hz';

        // Generate frequency response
        this.generateFrequencyResponse();
    }
    
    generateFrequencyResponse() {
        const frequencies = [];
        const magnitude = [];

        // Generate frequency points (logarithmic scale)
        for (let i = -1; i <= 3; i += 0.1) {
            const freq = Math.pow(10, i);
            frequencies.push(freq);
            
            // Calculate magnitude response for ECG amplifier
            const mag = this.calculateMagnitudeResponse(freq);
            magnitude.push(mag);
        }

        // Plot frequency response using Plotly
        if (typeof Plotly !== 'undefined') {
            const trace1 = {
                x: frequencies,
                y: magnitude,
                type: 'scatter',
                mode: 'lines',
                name: 'Magnitude',
                line: { color: '#667eea', width: 3 }
            };

            const layout = {
                title: 'ECG Amplifier Frequency Response',
                xaxis: {
                    title: 'Frequency (Hz)',
                    type: 'log',
                    range: [-1, 3]
                },
                yaxis: {
                    title: 'Magnitude (dB)',
                    range: [-60, 80]
                },
                grid: { rows: 1, columns: 1, pattern: 'independent' },
                showlegend: true,
                margin: { t: 50, r: 50, b: 50, l: 50 }
            };

            const plotElement = document.getElementById('frequencyResponse');
            if (plotElement) {
                Plotly.newPlot('frequencyResponse', [trace1], layout, {responsive: true});
            }
        }
    }
    
    calculateMagnitudeResponse(freq) {
        const gainElement = document.getElementById('gain');
        const gain = gainElement ? parseFloat(gainElement.value) : 1100;
        
        // High-pass filter (0.5 Hz cutoff)
        const hpf_fc = 0.5;
        const hpf_response = 20 * Math.log10(freq / Math.sqrt(Math.pow(hpf_fc, 2) + Math.pow(freq, 2)));
        
        // Low-pass filter (150 Hz cutoff, 4th order)
        const lpf_fc = 150;
        const lpf_response = -40 * Math.log10(Math.sqrt(1 + Math.pow(freq / lpf_fc, 8)));
        
        // Notch filter (50 Hz)
        const notch_fc = 50;
        const notch_q = 30;
        const notch_response = freq === notch_fc ? -40 : 
            -20 * Math.log10(Math.sqrt(1 + Math.pow(notch_q * (freq / notch_fc - notch_fc / freq), 2)));
        
        // Total gain in dB
        const total_gain_db = 20 * Math.log10(gain);
        
        return total_gain_db + hpf_response + lpf_response + notch_response;
    }
    
    // Schematic view switching
    switchSchematicView() {
        const viewElement = document.getElementById('schematicView');
        const view = viewElement ? viewElement.value : 'complete';
        this.currentSchematicView = view;
        
        const allGroups = ['inputStage', 'inaStage', 'filterStage', 'outputStage', 'adcStage', 'powerSupply', 'ground'];
        
        // Hide all groups first
        allGroups.forEach(groupId => {
            const group = document.getElementById(groupId);
            if (group) {
                group.style.opacity = '0.2';
            }
        });
        
        // Show selected stage(s)
        switch(view) {
            case 'complete':
                allGroups.forEach(groupId => {
                    const group = document.getElementById(groupId);
                    if (group) group.style.opacity = '1';
                });
                break;
            case 'stage1':
                ['inputStage', 'ground'].forEach(groupId => {
                    const group = document.getElementById(groupId);
                    if (group) group.style.opacity = '1';
                });
                break;
            case 'stage2':
                ['inaStage', 'powerSupply', 'ground'].forEach(groupId => {
                    const group = document.getElementById(groupId);
                    if (group) group.style.opacity = '1';
                });
                break;
            case 'stage3':
                ['filterStage', 'powerSupply', 'ground'].forEach(groupId => {
                    const group = document.getElementById(groupId);
                    if (group) group.style.opacity = '1';
                });
                break;
            case 'stage4':
                ['outputStage', 'adcStage', 'powerSupply', 'ground'].forEach(groupId => {
                    const group = document.getElementById(groupId);
                    if (group) group.style.opacity = '1';
                });
                break;
        }
    }
    
    // Calculator functions
    switchCalculator() {
        const typeElement = document.getElementById('calculatorType');
        const type = typeElement ? typeElement.value : 'gain';
        this.currentCalculator = type;
        const content = document.getElementById('calculatorContent');
        
        if (!content) return;
        
        switch(type) {
            case 'gain':
                content.innerHTML = this.createGainCalculator();
                break;
            case 'filter':
                content.innerHTML = this.createFilterCalculator();
                break;
            case 'power':
                content.innerHTML = this.createPowerCalculator();
                break;
            case 'noise':
                content.innerHTML = this.createNoiseCalculator();
                break;
        }
    }
}

// Global circuit designer instance
let circuitDesigner;

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    circuitDesigner = new CircuitDesigner();
    window.circuitDesigner = circuitDesigner;
});

// Global functions for HTML onclick handlers
function switchSchematicView() {
    if (circuitDesigner) {
        circuitDesigner.switchSchematicView();
    }
}

function switchCalculator() {
    if (circuitDesigner) {
        circuitDesigner.switchCalculator();
    }
}

function openCircuitDesigner() {
    if (typeof showNotification === 'function') {
        showNotification('Opening Virtual Workbench...', 'info');
    }
    setTimeout(() => {
        window.location.href = 'virtual-workbench.html';
    }, 1000);
}

function exportSchematic() {
    if (typeof showNotification === 'function') {
        showNotification('Schematic export feature coming soon!', 'info');
    }
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CircuitDesigner;
}
