/**
 * Error Configuration and Global Settings
 * Comprehensive error management configuration for ECG Signal System
 *
 * Author: Dr. <PERSON>
 * Institution: SUST-BME (Sudan University of Science and Technology - Biomedical Engineering)
 * Year: 2025
 * Copyright: <EMAIL>
 * Contact: +249912867327, +966538076790
 *
 * Advanced Error Management Features:
 * - Comprehensive error filtering
 * - Smart notification system
 * - Development mode support
 * - Performance optimization
 */

// Global error configuration
window.ECG_ERROR_CONFIG = {
    // Error reporting settings
    enableErrorReporting: true,
    enableConsoleLogging: true,
    enableNotifications: false, // Disabled by default to prevent spam

    // Error filtering settings
    ignoreMinorErrors: true,
    ignoreDuplicateErrors: true,
    maxErrorsPerSession: 50,

    // Notification settings
    maxNotifications: 3,
    notificationDuration: 5000,
    errorNotificationDuration: 8000,

    // Development mode settings
    isDevelopment: window.location.hostname === 'localhost' ||
                   window.location.hostname === '127.0.0.1' ||
                   window.location.hostname.includes('192.168'),

    // Ignored error patterns (comprehensive list)
    ignoredPatterns: [
        // Browser and extension errors
        'ResizeObserver loop limit exceeded',
        'ResizeObserver loop completed with undelivered notifications',
        'Non-Error promise rejection captured',
        'Script error',
        'Network request failed',
        'Loading chunk',
        'Loading CSS chunk',
        'ChunkLoadError',
        'webpackChunkName',

        // Network errors
        'net::ERR_',
        'ERR_INTERNET_DISCONNECTED',
        'ERR_NETWORK_CHANGED',
        'ERR_CONNECTION_REFUSED',
        'ERR_NAME_NOT_RESOLVED',
        'ERR_TIMED_OUT',
        'ERR_FAILED',
        'ERR_ABORTED',
        'ERR_BLOCKED_BY_CLIENT',
        'ERR_BLOCKED_BY_RESPONSE',

        // Resource loading errors
        'favicon',
        'manifest',
        'service-worker',
        'sw.js',
        'robots.txt',
        '.map',

        // Third-party services
        'analytics',
        'google',
        'gtag',
        'adsense',
        'facebook',
        'twitter',
        'linkedin',
        'instagram',
        'youtube',
        'disqus',
        'addthis',
        'sharethis',

        // CDN and library errors
        'cdn',
        'bootstrap',
        'jquery',
        'font-awesome',
        'fontawesome',
        'googleapis',
        'gstatic',
        'cloudflare',
        'jsdelivr',
        'unpkg',

        // Browser specific
        'chrome-extension',
        'moz-extension',
        'safari-extension',
        'edge-extension',
        'opera-extension',

        // Common warnings
        'deprecated',
        'warning',
        'console',
        'debug',
        'info',
        'log',
        'trace',

        // CORS and security
        'cors',
        'cross-origin',
        'mixed content',
        'insecure',
        'blocked',
        'csp',
        'content security policy',

        // Mobile specific
        'touch',
        'gesture',
        'orientation',
        'devicemotion',
        'deviceorientation',

        // Media errors
        'video',
        'audio',
        'media',
        'autoplay',
        'play()',

        // Performance
        'performance',
        'memory',
        'heap',
        'gc',
        'garbage collection'
    ],

    // Critical error patterns that should always be reported
    criticalPatterns: [
        'workbench.*initialization.*failed',
        'navigation.*manager.*undefined',
        'chart.*rendering.*failed',
        'ecg.*data.*processing.*error',
        'circuit.*simulation.*error',
        'component.*loading.*failed',
        'database.*connection.*failed',
        'authentication.*failed',
        'security.*violation',
        'permission.*denied'
    ],

    // Resource error patterns to ignore
    ignoredResources: [
        'favicon.ico',
        'apple-touch-icon',
        'manifest.json',
        'service-worker.js',
        'sw.js',
        'robots.txt',
        'sitemap.xml',
        '.map',
        'analytics',
        'gtag',
        'google',
        'facebook',
        'twitter',
        'linkedin',
        'instagram',
        'youtube'
    ]
};

// Enhanced error checking functions
window.ECG_ERROR_UTILS = {
    // Check if error should be ignored
    shouldIgnoreError: function(message, filename = '', source = '') {
        const config = window.ECG_ERROR_CONFIG;
        const fullMessage = `${message} ${filename} ${source}`.toLowerCase();

        return config.ignoredPatterns.some(pattern =>
            fullMessage.includes(pattern.toLowerCase())
        );
    },

    // Check if error is critical
    isCriticalError: function(message, filename = '', source = '') {
        const config = window.ECG_ERROR_CONFIG;
        const fullMessage = `${message} ${filename} ${source}`.toLowerCase();

        return config.criticalPatterns.some(pattern => {
            const regex = new RegExp(pattern, 'i');
            return regex.test(fullMessage);
        });
    },

    // Check if resource error should be ignored
    shouldIgnoreResourceError: function(src) {
        const config = window.ECG_ERROR_CONFIG;
        const srcLower = src.toLowerCase();

        return config.ignoredResources.some(resource =>
            srcLower.includes(resource.toLowerCase())
        );
    },

    // Sanitize error message for display
    sanitizeErrorMessage: function(message) {
        // Remove sensitive information
        return message
            .replace(/https?:\/\/[^\s]+/g, '[URL]')
            .replace(/\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/g, '[IP]')
            .replace(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g, '[EMAIL]')
            .replace(/\b[A-Za-z0-9]{20,}\b/g, '[TOKEN]');
    },

    // Get user-friendly error message
    getUserFriendlyMessage: function(error) {
        const message = error.message || error.toString();
        const messageLower = message.toLowerCase();

        if (messageLower.includes('network') || messageLower.includes('fetch')) {
            return 'Network connection issue. Please check your internet connection.';
        }

        if (messageLower.includes('permission') || messageLower.includes('blocked')) {
            return 'Permission denied. Please check your browser settings.';
        }

        if (messageLower.includes('not found') || messageLower.includes('404')) {
            return 'Resource not found. Some features may be temporarily unavailable.';
        }

        if (messageLower.includes('timeout') || messageLower.includes('timed out')) {
            return 'Request timed out. Please try again.';
        }

        if (messageLower.includes('workbench')) {
            return 'Virtual Workbench encountered an issue. Try refreshing the page.';
        }

        if (messageLower.includes('chart') || messageLower.includes('plot')) {
            return 'Chart rendering issue. Data visualization may be affected.';
        }

        return 'A minor system issue occurred. The application should continue to work normally.';
    }
};

// Initialize error configuration
if (window.ECG_ERROR_CONFIG.isDevelopment) {
    console.log('🔧 ECG Error Management - Development Mode');
    console.log('Error reporting:', window.ECG_ERROR_CONFIG.enableErrorReporting);
    console.log('Console logging:', window.ECG_ERROR_CONFIG.enableConsoleLogging);
    console.log('Notifications:', window.ECG_ERROR_CONFIG.enableNotifications);
}

// Export configuration for other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        ECG_ERROR_CONFIG: window.ECG_ERROR_CONFIG,
        ECG_ERROR_UTILS: window.ECG_ERROR_UTILS
    };
}

console.log('⚙️ ECG Error Configuration loaded');
