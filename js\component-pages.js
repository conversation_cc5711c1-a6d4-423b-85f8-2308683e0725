// Component Pages JavaScript

class ComponentPageManager {
    constructor() {
        this.currentPage = this.detectCurrentPage();
        this.initializePage();
    }
    
    detectCurrentPage() {
        const path = window.location.pathname;
        if (path.includes('ecg-electrodes')) return 'electrodes';
        if (path.includes('amplifier-design')) return 'amplifier';
        if (path.includes('analog-filters')) return 'filters';
        if (path.includes('adc-processing')) return 'adc';
        if (path.includes('digital-processing')) return 'processing';
        if (path.includes('data-analysis')) return 'analysis';
        return 'unknown';
    }
    
    initializePage() {
        this.setupInteractiveElements();
        this.loadPageSpecificContent();
        this.setupPlots();
    }
    
    setupInteractiveElements() {
        // Add hover effects to specification cards
        const specCards = document.querySelectorAll('.spec-card');
        specCards.forEach(card => {
            card.addEventListener('mouseenter', this.highlightCard);
            card.addEventListener('mouseleave', this.unhighlightCard);
        });
        
        // Add click handlers for troubleshooting cards
        const issueCards = document.querySelectorAll('.issue-card');
        issueCards.forEach(card => {
            card.addEventListener('click', this.toggleIssueDetails);
        });
        
        // Setup navigation animations
        this.setupNavigationAnimations();
    }
    
    highlightCard(event) {
        event.currentTarget.style.transform = 'translateY(-5px)';
        event.currentTarget.style.boxShadow = '0 15px 40px rgba(0,0,0,0.15)';
    }
    
    unhighlightCard(event) {
        event.currentTarget.style.transform = 'translateY(0)';
        event.currentTarget.style.boxShadow = '0 5px 15px rgba(0,0,0,0.1)';
    }
    
    toggleIssueDetails(event) {
        const card = event.currentTarget;
        const content = card.querySelector('.issue-content');
        
        if (card.classList.contains('expanded')) {
            card.classList.remove('expanded');
            content.style.maxHeight = '200px';
        } else {
            card.classList.add('expanded');
            content.style.maxHeight = content.scrollHeight + 'px';
        }
    }
    
    setupNavigationAnimations() {
        const navButtons = document.querySelectorAll('.nav-link-btn');
        navButtons.forEach(btn => {
            btn.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px)';
            });
            btn.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    }
    
    loadPageSpecificContent() {
        switch (this.currentPage) {
            case 'electrodes':
                this.loadElectrodesContent();
                break;
            case 'amplifier':
                this.loadAmplifierContent();
                break;
            case 'filters':
                this.loadFiltersContent();
                break;
            case 'adc':
                this.loadADCContent();
                break;
            case 'processing':
                this.loadProcessingContent();
                break;
            case 'analysis':
                this.loadAnalysisContent();
                break;
        }
    }
    
    loadElectrodesContent() {
        // Add electrode-specific animations
        this.animateElectrodePositions();
        
        // Update stats with real-time values
        this.updateElectrodeStats();
    }
    
    animateElectrodePositions() {
        const electrodes = document.querySelectorAll('.electrode-dot');
        electrodes.forEach((electrode, index) => {
            setTimeout(() => {
                electrode.style.animation = 'pulse 2s infinite';
                electrode.style.animationDelay = `${index * 0.5}s`;
            }, index * 200);
        });
        
        // Add CSS animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0%, 100% { transform: scale(1); box-shadow: 0 2px 8px rgba(0,0,0,0.2); }
                50% { transform: scale(1.1); box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4); }
            }
        `;
        document.head.appendChild(style);
    }
    
    updateElectrodeStats() {
        // Simulate real-time electrode impedance monitoring
        const stats = document.querySelectorAll('.stat-value');
        if (stats.length >= 3) {
            setInterval(() => {
                // Simulate slight variations in impedance
                const impedance = (1.8 + Math.random() * 0.4).toFixed(1);
                stats[1].textContent = `<${impedance}kΩ`;
                
                // Simulate offset voltage variations
                const offset = (80 + Math.random() * 40).toFixed(0);
                stats[2].textContent = `<${offset}μV`;
            }, 3000);
        }
    }
    
    setupPlots() {
        const plotContainer = document.getElementById('signalQualityPlot');
        if (plotContainer && typeof Plotly !== 'undefined') {
            this.createSignalQualityPlot(plotContainer);
        }
    }
    
    createSignalQualityPlot(container) {
        // Generate sample data showing electrode signal quality over time
        const time = Array.from({length: 100}, (_, i) => i * 0.1);
        const goodSignal = time.map(t => Math.sin(2 * Math.PI * 1.2 * t) + 0.1 * Math.random());
        const poorSignal = time.map(t => Math.sin(2 * Math.PI * 1.2 * t) + 0.5 * Math.random() + 0.3 * Math.sin(2 * Math.PI * 50 * t));
        
        const traces = [
            {
                x: time,
                y: goodSignal,
                type: 'scatter',
                mode: 'lines',
                name: 'Good Electrode Contact',
                line: { color: '#4CAF50', width: 2 }
            },
            {
                x: time,
                y: poorSignal,
                type: 'scatter',
                mode: 'lines',
                name: 'Poor Electrode Contact',
                line: { color: '#ff6b6b', width: 2 }
            }
        ];
        
        const layout = {
            title: {
                text: 'Signal Quality Comparison',
                font: { size: 16 }
            },
            xaxis: {
                title: 'Time (s)',
                showgrid: true,
                gridcolor: '#f0f0f0'
            },
            yaxis: {
                title: 'Amplitude (mV)',
                showgrid: true,
                gridcolor: '#f0f0f0'
            },
            legend: {
                x: 0,
                y: 1,
                bgcolor: 'rgba(255,255,255,0.8)'
            },
            margin: { t: 40, r: 20, b: 40, l: 50 },
            paper_bgcolor: 'white',
            plot_bgcolor: 'white'
        };
        
        const config = {
            displayModeBar: false,
            responsive: true
        };
        
        try {
            Plotly.newPlot(container, traces, layout, config);
        } catch (error) {
            console.warn('Could not create signal quality plot:', error);
            container.innerHTML = `
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">
                    <div style="text-align: center;">
                        <i class="fas fa-chart-line" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <p>Signal Quality Visualization</p>
                        <small>Interactive plot would show electrode signal quality comparison</small>
                    </div>
                </div>
            `;
        }
    }
    
    // Placeholder methods for other component pages
    loadAmplifierContent() {
        console.log('Loading amplifier-specific content...');
    }
    
    loadFiltersContent() {
        console.log('Loading filters-specific content...');
    }
    
    loadADCContent() {
        console.log('Loading ADC-specific content...');
    }
    
    loadProcessingContent() {
        console.log('Loading processing-specific content...');
    }
    
    loadAnalysisContent() {
        console.log('Loading analysis-specific content...');
    }
    
    // Utility methods
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-info-circle"></i>
                <span>${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#ff6b6b' : '#4CAF50'};
            color: white;
            padding: 1rem;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 300px;
            animation: slideIn 0.3s ease-out;
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
    
    updateBreadcrumb(pageName) {
        const currentBreadcrumb = document.querySelector('.breadcrumb-current');
        if (currentBreadcrumb) {
            currentBreadcrumb.textContent = pageName;
        }
    }
    
    // Performance monitoring
    measurePageLoad() {
        if (window.performance && window.performance.timing) {
            window.addEventListener('load', () => {
                setTimeout(() => {
                    const timing = window.performance.timing;
                    const loadTime = timing.loadEventEnd - timing.navigationStart;
                    console.log(`📊 ${this.currentPage} page load time: ${loadTime}ms`);
                    
                    if (loadTime > 2000) {
                        console.warn('⚠️ Slow page load detected');
                    }
                }, 100);
            });
        }
    }
}

// Initialize component page manager when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    try {
        window.componentPageManager = new ComponentPageManager();
        console.log('✅ Component page manager initialized');
    } catch (error) {
        console.error('Error initializing component page manager:', error);
    }
});

// Add notification styles
const notificationStyles = document.createElement('style');
notificationStyles.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    .notification-content {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }
    
    .notification-close {
        background: none;
        border: none;
        color: white;
        cursor: pointer;
        padding: 0.25rem;
        border-radius: 3px;
        margin-left: auto;
    }
    
    .notification-close:hover {
        background: rgba(255,255,255,0.2);
    }
    
    .issue-card.expanded .issue-content {
        transition: max-height 0.3s ease;
    }
    
    .issue-card .issue-content {
        max-height: 200px;
        overflow: hidden;
        transition: max-height 0.3s ease;
    }
`;
document.head.appendChild(notificationStyles);

// Export for global access
window.ComponentPageManager = ComponentPageManager;
