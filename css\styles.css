/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 1rem 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
}

.nav-logo i {
    margin-right: 10px;
    color: #ff6b6b;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.nav-link:hover,
.nav-link.active {
    background-color: rgba(255,255,255,0.2);
    color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.nav-link i {
    font-size: 0.9rem;
    transition: transform 0.3s ease;
}

.nav-link:hover i {
    transform: scale(1.1);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: white;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 120px 0 80px;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #fff, #ff6b6b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.hero-description {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.8;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
}

.btn-secondary {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
}

.btn-secondary:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

/* Hero Visual */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.ecg-preview {
    width: 100%;
    height: 300px;
    background: rgba(255,255,255,0.1);
    border-radius: 15px;
    padding: 20px;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

/* Sections */
.section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #333;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 2px;
}

/* Features Section */
.features {
    padding: 80px 0;
    background: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-card {
    text-align: center;
    padding: 2rem;
    border-radius: 15px;
    background: white;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
}

.feature-card.clickable {
    cursor: pointer;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.feature-action {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    opacity: 0;
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.feature-card.clickable:hover .feature-action {
    opacity: 1;
    transform: scale(1.1);
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

/* System Overview */
.system-overview {
    padding: 80px 0;
    background: #f8f9fa;
}

.system-diagram {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 2rem;
}

.system-block {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
    min-width: 150px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.system-block:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.system-block i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
    color: #667eea;
}

.system-block:hover i {
    color: white;
}

.arrow {
    font-size: 1.5rem;
    color: #667eea;
    font-weight: bold;
}

/* Quick Access */
.quick-access {
    padding: 80px 0;
    background: white;
}

.access-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.access-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
    cursor: pointer;
}

.access-card:hover {
    transform: translateY(-10px);
}

.access-icon {
    width: 100px;
    height: 100px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2.5rem;
}

/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    margin-bottom: 1rem;
    color: #ff6b6b;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #ff6b6b;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 2rem;
    border-radius: 15px;
    width: 90%;
    max-width: 800px;
    position: relative;
}

.close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    font-size: 2rem;
    cursor: pointer;
    color: #999;
}

.close:hover {
    color: #333;
}

/* Technical Specifications */
.technical-specs {
    padding: 80px 0;
    background: #f8f9fa;
}

.spec-category {
    margin-bottom: 4rem;
}

.spec-category h3 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.spec-category h3 i {
    color: #667eea;
}

.spec-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.spec-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-left: 4px solid #667eea;
}

.spec-card h4 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.spec-list {
    list-style: none;
    padding: 0;
}

.spec-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
    color: #666;
}

.spec-list li:last-child {
    border-bottom: none;
}

.spec-list strong {
    color: #333;
    font-weight: 600;
}

.component-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.component-detail-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
}

.component-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.component-header i {
    font-size: 1.5rem;
}

.component-header h4 {
    margin: 0;
    font-size: 1.2rem;
}

.component-specs {
    padding: 1.5rem;
}

.specs-table {
    width: 100%;
    border-collapse: collapse;
}

.specs-table td {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: top;
}

.specs-table td:first-child {
    font-weight: 600;
    color: #666;
    width: 45%;
}

.specs-table td:last-child {
    color: #333;
}

.specs-table tr:last-child td {
    border-bottom: none;
}

.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.filter-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-top: 4px solid #ff6b6b;
}

.filter-card h4 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.filter-details p {
    margin-bottom: 0.5rem;
    color: #666;
    line-height: 1.6;
}

.filter-details strong {
    color: #333;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: rgba(102, 126, 234, 0.95);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .system-diagram {
        flex-direction: column;
    }

    .arrow {
        transform: rotate(90deg);
    }

    .component-details-grid {
        grid-template-columns: 1fr;
    }

    .spec-grid {
        grid-template-columns: 1fr;
    }

    .filter-grid {
        grid-template-columns: 1fr;
    }

    /* Navigation responsive adjustments */
    .return-nav-button {
        left: 10px;
        padding: 10px 16px;
        font-size: 0.8rem;
    }

    .nav-history-panel {
        width: calc(100vw - 40px);
        left: -100vw;
    }

    .nav-history-panel.active {
        left: 20px;
    }

    .quick-nav-menu {
        bottom: 10px;
        right: 10px;
    }

    .quick-nav-toggle {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
    }

    .quick-nav-item {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }

    .breadcrumb-container {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .breadcrumb-nav-actions {
        width: 100%;
        justify-content: space-between;
    }
}

/* Additional styles for enhanced features */
.feature-range {
    color: #999;
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

.analysis-card .metric-details {
    margin: 1rem 0;
}

.analysis-card .metric-details div {
    margin-bottom: 0.5rem;
}

.analysis-card .metric-summary {
    margin-top: 1rem;
}

/* Analysis card specific styles */
.metric-status {
    margin-top: 1rem;
}

.metric-info {
    margin-top: 1rem;
}

.system-status {
    margin: 1rem 0;
}

.status-item {
    margin-bottom: 0.5rem;
}

.frequency-metrics {
    margin: 1rem 0;
}

.clinical-metrics {
    margin: 1rem 0;
}

.clinical-summary {
    margin-top: 1rem;
}

/* BOM Summary Styles */
.bom-summary {
    margin-bottom: 2rem;
}

.bom-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.stat-item {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* PCB Viewer specific styles */
.export-controls {
    margin-left: auto;
}

/* Diagram link styles */
.diagram-link {
    text-align: center;
    margin-top: 2rem;
}

.diagram-link p {
    margin-top: 0.5rem;
    color: #666;
}

/* Return Navigation Button Styles */
.return-nav-button {
    position: fixed;
    top: 50%;
    left: 20px;
    transform: translateY(-50%);
    z-index: 1500;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 50px;
    padding: 12px 20px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    opacity: 0.9;
}

.return-nav-button:hover {
    transform: translateY(-50%) translateX(5px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    opacity: 1;
}

.return-nav-button i {
    font-size: 1rem;
}

/* Navigation History Panel */
.nav-history-panel {
    position: fixed;
    top: 50%;
    left: -300px;
    transform: translateY(-50%);
    width: 280px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    z-index: 1400;
    transition: left 0.3s ease;
    max-height: 400px;
    overflow-y: auto;
}

.nav-history-panel.active {
    left: 20px;
}

.nav-history-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 1rem;
    border-radius: 15px 15px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-history-header h3 {
    margin: 0;
    font-size: 1rem;
}

.nav-history-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.nav-history-close:hover {
    background-color: rgba(255,255,255,0.2);
}

.nav-history-list {
    padding: 1rem;
}

.nav-history-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    text-decoration: none;
    color: #333;
    margin-bottom: 8px;
}

.nav-history-item:hover {
    background-color: #f8f9fa;
}

.nav-history-item i {
    color: #667eea;
    width: 20px;
    text-align: center;
}

.nav-history-item-content {
    flex: 1;
}

.nav-history-item-title {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 2px;
}

.nav-history-item-path {
    font-size: 0.8rem;
    color: #666;
}

/* Quick Navigation Menu */
.quick-nav-menu {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1500;
}

.quick-nav-toggle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
}

.quick-nav-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.quick-nav-items {
    position: absolute;
    bottom: 70px;
    right: 0;
    display: flex;
    flex-direction: column;
    gap: 10px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.quick-nav-menu.active .quick-nav-items {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.quick-nav-item {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: white;
    color: #667eea;
    border: 2px solid #667eea;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.quick-nav-item:hover {
    background: #667eea;
    color: white;
    transform: scale(1.1);
}

/* Breadcrumb Enhancement */
.enhanced-breadcrumb {
    background: white;
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
    position: sticky;
    top: 70px;
    z-index: 100;
}

.breadcrumb-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.breadcrumb-nav-actions {
    display: flex;
    gap: 10px;
}

.breadcrumb-action-btn {
    padding: 8px 16px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    color: #667eea;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.breadcrumb-action-btn:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* Notification System */
.notification-container {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 2000;
    max-width: 350px;
}

.notification {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    margin-bottom: 10px;
    padding: 1rem;
    border-left: 4px solid #667eea;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    gap: 12px;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left-color: #4CAF50;
}

.notification.warning {
    border-left-color: #ff9800;
}

.notification.error {
    border-left-color: #f44336;
}

.notification-icon {
    font-size: 1.2rem;
    color: #667eea;
}

.notification.success .notification-icon {
    color: #4CAF50;
}

.notification.warning .notification-icon {
    color: #ff9800;
}

.notification.error .notification-icon {
    color: #f44336;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    margin-bottom: 4px;
    color: #333;
}

.notification-message {
    color: #666;
    font-size: 0.9rem;
}

.notification-close {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    font-size: 1.1rem;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.notification-close:hover {
    background: #f5f5f5;
    color: #333;
}

/* Demo Card Styling */
.access-card.demo-card {
    border: 2px solid #ff6b6b;
}

.access-card.demo-card .access-icon {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

.access-card.demo-card .btn-outline {
    color: #ff6b6b;
    border-color: #ff6b6b;
}

.access-card.demo-card .btn-outline:hover {
    background: #ff6b6b;
    color: white;
}

/* Main Footer Styles */
.main-footer {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    padding: 40px 0 20px 0;
    margin-top: 60px;
    border-top: 4px solid #3498db;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.footer-section h3 {
    color: #3498db;
    font-size: 18px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.footer-section p {
    margin: 8px 0;
    color: #bdc3c7;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.footer-section i {
    color: #3498db;
    width: 16px;
    text-align: center;
}

.footer-bottom {
    background: rgba(0, 0, 0, 0.2);
    text-align: center;
    padding: 20px;
    margin-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
    margin: 0;
    color: #ecf0f1;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.footer-bottom .fa-heart {
    color: #e74c3c !important;
    animation: heartbeat 2s infinite;
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Responsive Footer */
@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: 30px;
        text-align: center;
    }

    .footer-section p {
        justify-content: center;
    }

    .footer-bottom p {
        flex-direction: column;
        gap: 4px;
    }
}
