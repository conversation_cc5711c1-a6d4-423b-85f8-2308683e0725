// Global Error Handler for ECG Signal System

class ErrorHandler {
    constructor() {
        this.errors = [];
        this.maxErrors = 50;
        this.setupErrorHandling();
    }

    setupErrorHandling() {
        // Handle JavaScript errors
        window.addEventListener('error', (event) => {
            this.logError({
                type: 'JavaScript Error',
                message: event.message,
                filename: event.filename,
                line: event.lineno,
                column: event.colno,
                stack: event.error ? event.error.stack : null,
                timestamp: new Date().toISOString()
            });
        });

        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.logError({
                type: 'Unhandled Promise Rejection',
                message: event.reason ? event.reason.toString() : 'Unknown promise rejection',
                stack: event.reason ? event.reason.stack : null,
                timestamp: new Date().toISOString()
            });
        });

        // Handle resource loading errors
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                this.logError({
                    type: 'Resource Loading Error',
                    message: `Failed to load: ${event.target.src || event.target.href}`,
                    element: event.target.tagName,
                    timestamp: new Date().toISOString()
                });
            }
        }, true);
    }

    logError(error) {
        // Add to error log
        this.errors.push(error);

        // Limit error log size
        if (this.errors.length > this.maxErrors) {
            this.errors.shift();
        }

        // Log to console
        console.error('ECG System Error:', error);

        // Show user-friendly notification for critical errors
        if (this.isCriticalError(error)) {
            this.showErrorNotification(error);
        }
    }

    isCriticalError(error) {
        const criticalPatterns = [
            /plotly/i,
            /cannot read property/i,
            /undefined is not a function/i,
            /network error/i,
            /failed to fetch/i,
            /workbench/i,
            /navigation/i,
            /chart/i,
            /ecg/i
        ];

        // Don't show notifications for minor console warnings
        const ignoredPatterns = [
            /favicon/i,
            /manifest/i,
            /service worker/i,
            /analytics/i
        ];

        const isIgnored = ignoredPatterns.some(pattern =>
            pattern.test(error.message) ||
            (error.stack && pattern.test(error.stack))
        );

        if (isIgnored) return false;

        return criticalPatterns.some(pattern =>
            pattern.test(error.message) ||
            (error.stack && pattern.test(error.stack))
        );
    }

    showErrorNotification(error) {
        // Prevent duplicate notifications
        if (document.querySelector('.error-notification')) {
            return;
        }

        // Create error notification
        const notification = document.createElement('div');
        notification.className = 'error-notification';
        notification.innerHTML = `
            <div class="error-content">
                <div class="error-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="error-message">
                    <h4>System Notice</h4>
                    <p>${this.getUserFriendlyMessage(error)}</p>
                    <div class="error-actions">
                        <button class="error-action-btn" onclick="errorHandler.handleErrorAction('${error.type}')">
                            <i class="fas fa-tools"></i> Fix Issue
                        </button>
                        <button class="error-action-btn secondary" onclick="errorHandler.dismissError(this)">
                            <i class="fas fa-times"></i> Dismiss
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Style the notification
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 400px;
            animation: slideIn 0.3s ease-out;
        `;

        // Add styles for notification content
        const style = document.createElement('style');
        style.textContent = `
            .error-content {
                display: flex;
                align-items: flex-start;
                padding: 1rem;
                gap: 0.75rem;
            }
            .error-icon {
                color: #856404;
                font-size: 1.2rem;
                margin-top: 0.2rem;
                flex-shrink: 0;
            }
            .error-message {
                flex: 1;
            }
            .error-message h4 {
                margin: 0 0 0.5rem 0;
                color: #856404;
                font-size: 1rem;
                font-weight: 600;
            }
            .error-message p {
                margin: 0 0 1rem 0;
                color: #856404;
                font-size: 0.9rem;
                line-height: 1.4;
            }
            .error-actions {
                display: flex;
                gap: 0.5rem;
                margin-top: 0.75rem;
            }
            .error-action-btn {
                background: #856404;
                color: white;
                border: none;
                padding: 0.5rem 0.75rem;
                border-radius: 4px;
                font-size: 0.8rem;
                cursor: pointer;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                gap: 0.25rem;
            }
            .error-action-btn:hover {
                background: #6c5003;
                transform: translateY(-1px);
            }
            .error-action-btn.secondary {
                background: transparent;
                color: #856404;
                border: 1px solid #856404;
            }
            .error-action-btn.secondary:hover {
                background: #856404;
                color: white;
            }
            .modal-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                backdrop-filter: blur(2px);
            }
            .modal-content {
                background: white;
                border-radius: 12px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                max-width: 500px;
                width: 90%;
                max-height: 80vh;
                overflow-y: auto;
                position: relative;
                z-index: 1;
            }
            .modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 1.5rem;
                border-bottom: 1px solid #eee;
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white;
                border-radius: 12px 12px 0 0;
            }
            .modal-header h3 {
                margin: 0;
                font-size: 1.1rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }
            .modal-close {
                background: none;
                border: none;
                color: white;
                cursor: pointer;
                font-size: 1.2rem;
                padding: 0.5rem;
                border-radius: 4px;
                transition: background 0.3s ease;
            }
            .modal-close:hover {
                background: rgba(255,255,255,0.2);
            }
            .modal-body {
                padding: 1.5rem;
            }
            .modal-body p {
                margin: 0 0 1.5rem 0;
                color: #666;
                line-height: 1.6;
            }
            .solution-actions {
                display: flex;
                gap: 1rem;
                justify-content: flex-end;
            }
            .btn {
                padding: 0.75rem 1.5rem;
                border: none;
                border-radius: 6px;
                font-size: 0.9rem;
                cursor: pointer;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-weight: 500;
            }
            .btn-primary {
                background: #667eea;
                color: white;
            }
            .btn-primary:hover {
                background: #5a6fd8;
                transform: translateY(-1px);
            }
            .btn-secondary {
                background: #f8f9fa;
                color: #666;
                border: 1px solid #dee2e6;
            }
            .btn-secondary:hover {
                background: #e9ecef;
                color: #333;
            }
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
            @keyframes slideOut {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;

        if (!document.querySelector('#error-notification-styles')) {
            style.id = 'error-notification-styles';
            document.head.appendChild(style);
        }

        document.body.appendChild(notification);

        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 10000);
    }

    getUserFriendlyMessage(error) {
        const message = error.message.toLowerCase();

        if (message.includes('plotly')) {
            return 'Chart visualization temporarily unavailable. Basic functionality remains active.';
        }

        if (message.includes('workbench')) {
            return 'Virtual Workbench encountered an issue. Try refreshing or using the sample circuit.';
        }

        if (message.includes('navigation')) {
            return 'Navigation system issue detected. Use browser back button or refresh the page.';
        }

        if (message.includes('ecg') || message.includes('signal')) {
            return 'ECG signal processing encountered an issue. Data visualization may be affected.';
        }

        if (error.type === 'Resource Loading Error') {
            return 'Some resources failed to load. Check your internet connection and refresh the page.';
        }

        if (message.includes('network') || message.includes('fetch')) {
            return 'Network connectivity issue detected. Some features may be limited.';
        }

        if (message.includes('cannot read property') || message.includes('undefined')) {
            return 'A component initialization issue occurred. The system is attempting to recover.';
        }

        return 'A minor system issue was detected. The application should continue to function normally.';
    }

    handleErrorAction(errorType) {
        switch (errorType) {
            case 'JavaScript Error':
                this.showErrorSolution('Try refreshing the page. If the issue persists, clear your browser cache.');
                break;
            case 'Resource Loading Error':
                this.showErrorSolution('Check your internet connection and refresh the page.');
                break;
            case 'Unhandled Promise Rejection':
                this.showErrorSolution('This is a minor issue. The system should continue working normally.');
                break;
            default:
                this.showErrorSolution('Try refreshing the page or contact support if the issue persists.');
        }
    }

    showErrorSolution(solution) {
        // Create solution modal
        const modal = document.createElement('div');
        modal.className = 'error-solution-modal';
        modal.innerHTML = `
            <div class="modal-overlay" onclick="this.parentElement.remove()"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-lightbulb"></i> Suggested Solution</h3>
                    <button class="modal-close" onclick="this.parentElement.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <p>${solution}</p>
                    <div class="solution-actions">
                        <button class="btn btn-primary" onclick="location.reload()">
                            <i class="fas fa-refresh"></i> Refresh Page
                        </button>
                        <button class="btn btn-secondary" onclick="errorHandler.clearErrors(); this.parentElement.parentElement.parentElement.remove()">
                            <i class="fas fa-check"></i> Mark as Resolved
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Add modal styles
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 10001;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        document.body.appendChild(modal);
    }

    dismissError(button) {
        const notification = button.closest('.error-notification');
        if (notification) {
            notification.style.animation = 'slideOut 0.3s ease-in forwards';
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }
    }

    getErrorReport() {
        return {
            totalErrors: this.errors.length,
            errors: this.errors,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };
    }

    clearErrors() {
        this.errors = [];
    }

    // Check for common issues and provide solutions
    performSystemCheck() {
        const issues = [];

        // Check if required libraries are loaded
        if (typeof Plotly === 'undefined') {
            issues.push({
                type: 'Missing Library',
                message: 'Plotly.js library not loaded',
                solution: 'Chart features will use fallback displays'
            });
        }

        // Check if required DOM elements exist
        const requiredElements = ['ecg-preview', 'componentModal'];
        requiredElements.forEach(id => {
            if (!document.getElementById(id)) {
                issues.push({
                    type: 'Missing Element',
                    message: `Required element '${id}' not found`,
                    solution: 'Some features may not be available'
                });
            }
        });

        // Check browser compatibility
        if (!window.fetch) {
            issues.push({
                type: 'Browser Compatibility',
                message: 'Fetch API not supported',
                solution: 'Please use a modern browser'
            });
        }

        if (issues.length > 0) {
            console.warn('System Check Issues:', issues);
        } else {
            console.log('System Check: All systems operational');
        }

        return issues;
    }

    // Automatic error recovery system
    attemptAutoRecovery(error) {
        const message = error.message.toLowerCase();

        // Try to recover from common issues
        if (message.includes('workbench') && window.workbench) {
            try {
                console.log('🔄 Attempting workbench recovery...');
                // Reinitialize workbench if possible
                if (typeof VirtualWorkbench !== 'undefined') {
                    setTimeout(() => {
                        window.workbench = new VirtualWorkbench();
                        console.log('✅ Workbench recovery successful');
                    }, 1000);
                }
            } catch (recoveryError) {
                console.warn('❌ Workbench recovery failed:', recoveryError);
            }
        }

        if (message.includes('navigation') && window.navigationManager) {
            try {
                console.log('🔄 Attempting navigation recovery...');
                // Reinitialize navigation if possible
                if (typeof NavigationManager !== 'undefined') {
                    setTimeout(() => {
                        window.navigationManager = new NavigationManager();
                        console.log('✅ Navigation recovery successful');
                    }, 1000);
                }
            } catch (recoveryError) {
                console.warn('❌ Navigation recovery failed:', recoveryError);
            }
        }

        // Clear any stuck modals or overlays
        if (message.includes('modal') || message.includes('overlay')) {
            try {
                document.querySelectorAll('.modal, .overlay, .error-notification').forEach(el => {
                    if (el.style.display !== 'none') {
                        el.remove();
                    }
                });
                console.log('✅ Cleared stuck UI elements');
            } catch (recoveryError) {
                console.warn('❌ UI cleanup failed:', recoveryError);
            }
        }
    }

    // Enhanced error logging with context
    logErrorWithContext(error, context = {}) {
        const enhancedError = {
            ...error,
            context: {
                url: window.location.href,
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString(),
                viewport: {
                    width: window.innerWidth,
                    height: window.innerHeight
                },
                ...context
            }
        };

        this.logError(enhancedError);

        // Attempt automatic recovery for certain errors
        this.attemptAutoRecovery(error);
    }

    // Create a safe function wrapper that catches errors
    createSafeFunction(fn, context = 'Unknown') {
        return (...args) => {
            try {
                return fn.apply(this, args);
            } catch (error) {
                this.logErrorWithContext({
                    type: 'Safe Function Error',
                    message: error.message,
                    stack: error.stack,
                    context: { functionContext: context }
                });

                // Return a safe fallback
                return null;
            }
        };
    }

    // Monitor system health
    startHealthMonitoring() {
        setInterval(() => {
            this.performHealthCheck();
        }, 30000); // Check every 30 seconds
    }

    performHealthCheck() {
        const healthStatus = {
            timestamp: new Date().toISOString(),
            errors: this.errors.length,
            memory: performance.memory ? {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024)
            } : null,
            navigation: !!window.navigationManager,
            workbench: !!window.workbench,
            plotly: typeof Plotly !== 'undefined'
        };

        // Log health status in development mode
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            console.log('🏥 Health Check:', healthStatus);
        }

        // Alert if too many errors
        if (this.errors.length > 10) {
            console.warn('⚠️ High error count detected:', this.errors.length);
            this.showHealthWarning();
        }

        return healthStatus;
    }

    showHealthWarning() {
        if (document.querySelector('.health-warning')) return;

        const warning = document.createElement('div');
        warning.className = 'health-warning error-notification';
        warning.innerHTML = `
            <div class="error-content">
                <div class="error-icon">
                    <i class="fas fa-heartbeat"></i>
                </div>
                <div class="error-message">
                    <h4>System Health Notice</h4>
                    <p>Multiple issues detected. Consider refreshing the page for optimal performance.</p>
                    <div class="error-actions">
                        <button class="error-action-btn" onclick="location.reload()">
                            <i class="fas fa-refresh"></i> Refresh Page
                        </button>
                        <button class="error-action-btn secondary" onclick="errorHandler.clearErrors(); this.parentElement.parentElement.parentElement.remove()">
                            <i class="fas fa-check"></i> Continue
                        </button>
                    </div>
                </div>
            </div>
        `;

        warning.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 400px;
            animation: slideIn 0.3s ease-out;
        `;

        document.body.appendChild(warning);
    }
}

// Initialize error handler
const errorHandler = new ErrorHandler();

// Perform system check after page load
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        errorHandler.performSystemCheck();
        errorHandler.startHealthMonitoring();
    }, 1000);
});

// Export for global access
window.errorHandler = errorHandler;

// Add helpful console messages
console.log('%c🫀 ECG Signal System Loaded', 'color: #ff6b6b; font-size: 16px; font-weight: bold;');
console.log('%cSystem Status: Initializing...', 'color: #667eea; font-size: 12px;');

// Check for development mode
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    console.log('%c🔧 Development Mode Active', 'color: #ffa500; font-size: 12px;');
    console.log('Error reporting enabled. Check errorHandler.getErrorReport() for details.');
}
