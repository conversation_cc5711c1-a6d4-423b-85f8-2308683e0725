// Global Error Handler for ECG Signal System

class ErrorHandler {
    constructor() {
        this.errors = [];
        this.maxErrors = 50;
        this.setupErrorHandling();
    }

    setupErrorHandling() {
        // Handle JavaScript errors with filtering
        window.addEventListener('error', (event) => {
            // Filter out common non-critical errors
            if (this.shouldIgnoreError(event.message, event.filename)) {
                return;
            }

            this.logError({
                type: 'JavaScript Error',
                message: event.message,
                filename: event.filename,
                line: event.lineno,
                column: event.colno,
                stack: event.error ? event.error.stack : null,
                timestamp: new Date().toISOString()
            });
        });

        // Handle unhandled promise rejections with filtering
        window.addEventListener('unhandledrejection', (event) => {
            const reason = event.reason ? event.reason.toString() : 'Unknown promise rejection';

            // Filter out common promise rejections
            if (this.shouldIgnoreError(reason)) {
                event.preventDefault(); // Prevent console logging
                return;
            }

            this.logError({
                type: 'Unhandled Promise Rejection',
                message: reason,
                stack: event.reason ? event.reason.stack : null,
                timestamp: new Date().toISOString()
            });
        });

        // Handle resource loading errors with filtering
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                const src = event.target.src || event.target.href;

                // Filter out common resource loading errors
                if (this.shouldIgnoreResourceError(src)) {
                    return;
                }

                this.logError({
                    type: 'Resource Loading Error',
                    message: `Failed to load: ${src}`,
                    element: event.target.tagName,
                    timestamp: new Date().toISOString()
                });
            }
        }, true);

        // Override console.error to filter out unwanted messages
        this.setupConsoleFiltering();
    }

    shouldIgnoreError(message, filename = '') {
        const ignoredMessages = [
            'ResizeObserver loop limit exceeded',
            'Non-Error promise rejection captured',
            'Script error',
            'Network request failed',
            'Loading chunk',
            'Loading CSS chunk',
            'ChunkLoadError',
            'webpackChunkName',
            'net::ERR_',
            'ERR_INTERNET_DISCONNECTED',
            'ERR_NETWORK_CHANGED',
            'ERR_CONNECTION_REFUSED',
            'ERR_NAME_NOT_RESOLVED',
            'favicon',
            'manifest',
            'service worker',
            'analytics',
            'google',
            'gtag',
            'adsense',
            'facebook',
            'twitter',
            'linkedin',
            'instagram',
            'youtube',
            'cdn',
            'bootstrap',
            'jquery',
            'font-awesome'
        ];

        return ignoredMessages.some(ignored =>
            message.toLowerCase().includes(ignored.toLowerCase()) ||
            filename.toLowerCase().includes(ignored.toLowerCase())
        );
    }

    shouldIgnoreResourceError(src) {
        const ignoredResources = [
            'favicon',
            'manifest',
            'service-worker',
            'sw.js',
            'analytics',
            'google',
            'gtag',
            'adsense',
            'facebook',
            'twitter',
            'linkedin',
            'instagram',
            'youtube'
        ];

        return ignoredResources.some(ignored =>
            src.toLowerCase().includes(ignored.toLowerCase())
        );
    }

    setupConsoleFiltering() {
        const originalError = console.error;
        const originalWarn = console.warn;

        console.error = (...args) => {
            const message = args.join(' ');
            if (!this.shouldIgnoreError(message)) {
                originalError.apply(console, args);
            }
        };

        console.warn = (...args) => {
            const message = args.join(' ');
            if (!this.shouldIgnoreError(message)) {
                originalWarn.apply(console, args);
            }
        };
    }

    logError(error) {
        // Add to error log
        this.errors.push(error);

        // Limit error log size
        if (this.errors.length > this.maxErrors) {
            this.errors.shift();
        }

        // Log to console
        console.error('ECG System Error:', error);

        // Show user-friendly notification for critical errors
        if (this.isCriticalError(error)) {
            this.showErrorNotification(error);
        }
    }

    isCriticalError(error) {
        // Don't show notifications for minor console warnings and common issues
        const ignoredPatterns = [
            /favicon/i,
            /manifest/i,
            /service worker/i,
            /analytics/i,
            /google/i,
            /gtag/i,
            /adsense/i,
            /facebook/i,
            /twitter/i,
            /linkedin/i,
            /instagram/i,
            /youtube/i,
            /cdn/i,
            /bootstrap/i,
            /jquery/i,
            /font-awesome/i,
            /css.*not.*loaded/i,
            /script.*not.*loaded/i,
            /image.*not.*found/i,
            /404/i,
            /403/i,
            /cors/i,
            /cross-origin/i,
            /mixed content/i,
            /insecure/i,
            /deprecated/i,
            /warning/i,
            /console/i,
            /debug/i,
            /info/i,
            /log/i,
            /ResizeObserver/i,
            /Non-Error promise rejection/i,
            /Script error/i,
            /Loading chunk/i,
            /Loading CSS chunk/i,
            /net::ERR_/i,
            /ERR_INTERNET_DISCONNECTED/i,
            /ERR_NETWORK_CHANGED/i,
            /ERR_CONNECTION_REFUSED/i,
            /ERR_NAME_NOT_RESOLVED/i,
            /ChunkLoadError/i,
            /webpackChunkName/i
        ];

        const isIgnored = ignoredPatterns.some(pattern =>
            pattern.test(error.message) ||
            (error.stack && pattern.test(error.stack)) ||
            (error.filename && pattern.test(error.filename))
        );

        if (isIgnored) return false;

        // Only show critical errors that actually affect functionality
        const criticalPatterns = [
            /workbench.*initialization.*failed/i,
            /navigation.*manager.*undefined/i,
            /chart.*rendering.*failed/i,
            /ecg.*data.*processing.*error/i,
            /circuit.*simulation.*error/i,
            /component.*loading.*failed/i
        ];

        return criticalPatterns.some(pattern =>
            pattern.test(error.message) ||
            (error.stack && pattern.test(error.stack))
        );
    }

    showErrorNotification(error) {
        // Create error notification
        const notification = document.createElement('div');
        notification.className = 'error-notification';
        notification.innerHTML = `
            <div class="error-content">
                <div class="error-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="error-message">
                    <h4>System Notice</h4>
                    <p>${this.getUserFriendlyMessage(error)}</p>
                </div>
                <button class="error-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // Style the notification
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            max-width: 400px;
            animation: slideIn 0.3s ease-out;
        `;

        // Add styles for notification content
        const style = document.createElement('style');
        style.textContent = `
            .error-content {
                display: flex;
                align-items: flex-start;
                padding: 1rem;
                gap: 0.75rem;
            }
            .error-icon {
                color: #856404;
                font-size: 1.2rem;
                margin-top: 0.2rem;
            }
            .error-message h4 {
                margin: 0 0 0.5rem 0;
                color: #856404;
                font-size: 1rem;
            }
            .error-message p {
                margin: 0;
                color: #856404;
                font-size: 0.9rem;
                line-height: 1.4;
            }
            .error-close {
                background: none;
                border: none;
                color: #856404;
                cursor: pointer;
                font-size: 1rem;
                padding: 0.25rem;
                border-radius: 4px;
                margin-left: auto;
            }
            .error-close:hover {
                background: rgba(133, 100, 4, 0.1);
            }
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;

        if (!document.querySelector('#error-notification-styles')) {
            style.id = 'error-notification-styles';
            document.head.appendChild(style);
        }

        document.body.appendChild(notification);

        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 10000);
    }

    getUserFriendlyMessage(error) {
        if (error.message.toLowerCase().includes('plotly')) {
            return 'Some chart features may not be available. The system will continue to work with basic functionality.';
        }

        if (error.type === 'Resource Loading Error') {
            return 'Some resources failed to load. Please check your internet connection and refresh the page.';
        }

        if (error.message.toLowerCase().includes('network')) {
            return 'Network connectivity issue detected. Some features may be limited.';
        }

        return 'A minor system issue was detected. The application should continue to function normally.';
    }

    getErrorReport() {
        return {
            totalErrors: this.errors.length,
            errors: this.errors,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };
    }

    clearErrors() {
        this.errors = [];
    }

    // Check for common issues and provide solutions
    performSystemCheck() {
        const issues = [];

        // Check if required libraries are loaded
        if (typeof Plotly === 'undefined') {
            issues.push({
                type: 'Missing Library',
                message: 'Plotly.js library not loaded',
                solution: 'Chart features will use fallback displays'
            });
        }

        // Check if required DOM elements exist
        const requiredElements = ['ecg-preview', 'componentModal'];
        requiredElements.forEach(id => {
            if (!document.getElementById(id)) {
                issues.push({
                    type: 'Missing Element',
                    message: `Required element '${id}' not found`,
                    solution: 'Some features may not be available'
                });
            }
        });

        // Check browser compatibility
        if (!window.fetch) {
            issues.push({
                type: 'Browser Compatibility',
                message: 'Fetch API not supported',
                solution: 'Please use a modern browser'
            });
        }

        if (issues.length > 0) {
            console.warn('System Check Issues:', issues);
        } else {
            console.log('System Check: All systems operational');
        }

        return issues;
    }
}

// Initialize error handler
const errorHandler = new ErrorHandler();

// Perform system check after page load
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        errorHandler.performSystemCheck();
    }, 1000);
});

// Export for global access
window.errorHandler = errorHandler;

// Add helpful console messages
console.log('%c🫀 ECG Signal System Loaded', 'color: #ff6b6b; font-size: 16px; font-weight: bold;');
console.log('%cSystem Status: Initializing...', 'color: #667eea; font-size: 12px;');

// Check for development mode
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    console.log('%c🔧 Development Mode Active', 'color: #ffa500; font-size: 12px;');
    console.log('Error reporting enabled. Check errorHandler.getErrorReport() for details.');
}
