/**
 * 3D Diagram Engine
 * Three.js-based 3D visualization for ECG System Components
 * 
 * Author: Dr. <PERSON>
 * Institution: SUST-BME (Sudan University of Science and Technology - Biomedical Engineering)
 * Year: 2025
 * Copyright: <EMAIL>
 * Contact: +249912867327, +966538076790
 * 
 * Features:
 * - 3D component visualization
 * - Interactive camera controls
 * - Component highlighting
 * - Signal flow animation
 * - PCB layout visualization
 */

class ThreeDDiagramEngine {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.components = new Map();
        this.connections = [];
        this.animationId = null;
        
        this.colors = {
            pcb: 0x2d5016,
            copper: 0xb87333,
            component: 0x404040,
            ic: 0x1a1a1a,
            resistor: 0x8b4513,
            capacitor: 0x4169e1,
            connector: 0xffd700
        };
        
        this.init();
    }
    
    init() {
        try {
            this.setupScene();
            this.setupCamera();
            this.setupRenderer();
            this.setupControls();
            this.setupLighting();
            this.createECGSystem3D();
            this.startAnimation();
            
            console.log('✅ 3D Diagram Engine initialized successfully');
        } catch (error) {
            console.error('❌ 3D Diagram Engine initialization failed:', error);
        }
    }
    
    setupScene() {
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x1a1a1a);
        this.scene.fog = new THREE.Fog(0x1a1a1a, 10, 50);
    }
    
    setupCamera() {
        const container = document.getElementById('threeDContainer');
        const width = container ? container.clientWidth : 800;
        const height = container ? container.clientHeight : 600;
        
        this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
        this.camera.position.set(15, 10, 15);
        this.camera.lookAt(0, 0, 0);
    }
    
    setupRenderer() {
        const container = document.getElementById('threeDContainer');
        if (!container) return;
        
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(container.clientWidth, container.clientHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.outputEncoding = THREE.sRGBEncoding;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.2;
        
        container.appendChild(this.renderer.domElement);
        
        // Handle window resize
        window.addEventListener('resize', () => this.handleResize());
    }
    
    setupControls() {
        if (typeof THREE.OrbitControls !== 'undefined') {
            this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
            this.controls.enableDamping = true;
            this.controls.dampingFactor = 0.05;
            this.controls.maxDistance = 50;
            this.controls.minDistance = 5;
        }
    }
    
    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);
        
        // Main directional light
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 50;
        this.scene.add(directionalLight);
        
        // Fill light
        const fillLight = new THREE.DirectionalLight(0x4080ff, 0.3);
        fillLight.position.set(-5, 5, -5);
        this.scene.add(fillLight);
        
        // Rim light
        const rimLight = new THREE.DirectionalLight(0xff8040, 0.2);
        rimLight.position.set(0, -5, 10);
        this.scene.add(rimLight);
    }
    
    createECGSystem3D() {
        // Create PCB base
        this.createPCB();
        
        // Create components
        this.createComponent('INA128', -4, 0.2, 2, 'ic');
        this.createComponent('OPA2131', 0, 0.2, 2, 'ic');
        this.createComponent('STM32F4', 4, 0.2, 0, 'ic', 2, 2);
        this.createComponent('A0509S', -2, 0.2, -3, 'ic');
        
        // Create passive components
        this.createResistor(-6, 0.1, 0, 47); // Gain resistor
        this.createCapacitor(-4, 0.1, -1, 100); // Coupling capacitor
        this.createCapacitor(2, 0.1, -1, 10); // Filter capacitor
        
        // Create connectors
        this.createConnector(-8, 0.2, 2, 'electrode');
        this.createConnector(-8, 0.2, 0, 'electrode');
        this.createConnector(-8, 0.2, -2, 'electrode');
        
        // Create connections
        this.createConnections();
        
        // Add labels
        this.addComponentLabels();
    }
    
    createPCB() {
        const pcbGeometry = new THREE.BoxGeometry(16, 0.2, 10);
        const pcbMaterial = new THREE.MeshLambertMaterial({ color: this.colors.pcb });
        const pcb = new THREE.Mesh(pcbGeometry, pcbMaterial);
        pcb.position.set(0, -0.1, 0);
        pcb.receiveShadow = true;
        this.scene.add(pcb);
        
        // Add copper traces
        this.createCopperTraces();
    }
    
    createCopperTraces() {
        const traceGeometry = new THREE.BoxGeometry(0.1, 0.01, 8);
        const traceMaterial = new THREE.MeshLambertMaterial({ color: this.colors.copper });
        
        // Main signal traces
        for (let i = -3; i <= 3; i += 2) {
            const trace = new THREE.Mesh(traceGeometry, traceMaterial);
            trace.position.set(i, 0.01, 0);
            this.scene.add(trace);
        }
        
        // Power traces
        const powerTraceGeometry = new THREE.BoxGeometry(12, 0.01, 0.2);
        const powerTrace1 = new THREE.Mesh(powerTraceGeometry, traceMaterial);
        powerTrace1.position.set(0, 0.01, 3);
        this.scene.add(powerTrace1);
        
        const powerTrace2 = new THREE.Mesh(powerTraceGeometry, traceMaterial);
        powerTrace2.position.set(0, 0.01, -3);
        this.scene.add(powerTrace2);
    }
    
    createComponent(name, x, y, z, type, width = 1, height = 1) {
        let geometry, material;
        
        switch (type) {
            case 'ic':
                geometry = new THREE.BoxGeometry(width, 0.3, height);
                material = new THREE.MeshPhongMaterial({ 
                    color: this.colors.ic,
                    shininess: 30
                });
                break;
            default:
                geometry = new THREE.BoxGeometry(0.8, 0.2, 0.8);
                material = new THREE.MeshLambertMaterial({ color: this.colors.component });
        }
        
        const component = new THREE.Mesh(geometry, material);
        component.position.set(x, y, z);
        component.castShadow = true;
        component.userData = { name, type };
        
        // Add click handler
        component.addEventListener = (event, handler) => {
            // Placeholder for click handling
        };
        
        this.scene.add(component);
        this.components.set(name, component);
        
        // Add pins for ICs
        if (type === 'ic') {
            this.addComponentPins(component, width, height);
        }
        
        return component;
    }
    
    addComponentPins(component, width, height) {
        const pinGeometry = new THREE.CylinderGeometry(0.02, 0.02, 0.1);
        const pinMaterial = new THREE.MeshPhongMaterial({ 
            color: 0xc0c0c0,
            shininess: 100
        });
        
        const pinsPerSide = 4;
        const pinSpacing = width / (pinsPerSide + 1);
        
        // Top and bottom pins
        for (let i = 0; i < pinsPerSide; i++) {
            // Top pins
            const topPin = new THREE.Mesh(pinGeometry, pinMaterial);
            topPin.position.set(
                component.position.x - width/2 + pinSpacing * (i + 1),
                component.position.y - 0.2,
                component.position.z + height/2
            );
            this.scene.add(topPin);
            
            // Bottom pins
            const bottomPin = new THREE.Mesh(pinGeometry, pinMaterial);
            bottomPin.position.set(
                component.position.x - width/2 + pinSpacing * (i + 1),
                component.position.y - 0.2,
                component.position.z - height/2
            );
            this.scene.add(bottomPin);
        }
    }
    
    createResistor(x, y, z, value) {
        const bodyGeometry = new THREE.CylinderGeometry(0.1, 0.1, 0.8);
        const bodyMaterial = new THREE.MeshLambertMaterial({ color: this.colors.resistor });
        const resistor = new THREE.Mesh(bodyGeometry, bodyMaterial);
        resistor.position.set(x, y, z);
        resistor.rotation.z = Math.PI / 2;
        resistor.castShadow = true;
        resistor.userData = { name: `R_${value}Ω`, type: 'resistor', value };
        
        // Add color bands
        this.addResistorBands(resistor, value);
        
        this.scene.add(resistor);
        return resistor;
    }
    
    addResistorBands(resistor, value) {
        const bandGeometry = new THREE.RingGeometry(0.1, 0.12, 8);
        const colors = this.getResistorColors(value);
        
        colors.forEach((color, index) => {
            const bandMaterial = new THREE.MeshLambertMaterial({ color });
            const band = new THREE.Mesh(bandGeometry, bandMaterial);
            band.position.copy(resistor.position);
            band.position.x += (index - 1.5) * 0.15;
            band.rotation.y = Math.PI / 2;
            this.scene.add(band);
        });
    }
    
    getResistorColors(value) {
        // Simplified resistor color code
        if (value === 47) return [0xffff00, 0x9400d3, 0x000000]; // Yellow, Violet, Black
        return [0x8b4513, 0x8b4513, 0x8b4513]; // Default brown
    }
    
    createCapacitor(x, y, z, value) {
        const bodyGeometry = new THREE.CylinderGeometry(0.15, 0.15, 0.4);
        const bodyMaterial = new THREE.MeshPhongMaterial({ 
            color: this.colors.capacitor,
            shininess: 50
        });
        const capacitor = new THREE.Mesh(bodyGeometry, bodyMaterial);
        capacitor.position.set(x, y, z);
        capacitor.castShadow = true;
        capacitor.userData = { name: `C_${value}nF`, type: 'capacitor', value };
        
        this.scene.add(capacitor);
        return capacitor;
    }
    
    createConnector(x, y, z, type) {
        const connectorGeometry = new THREE.CylinderGeometry(0.2, 0.2, 0.3);
        const connectorMaterial = new THREE.MeshPhongMaterial({ 
            color: this.colors.connector,
            shininess: 100
        });
        const connector = new THREE.Mesh(connectorGeometry, connectorMaterial);
        connector.position.set(x, y, z);
        connector.castShadow = true;
        connector.userData = { name: type, type: 'connector' };
        
        this.scene.add(connector);
        return connector;
    }
    
    createConnections() {
        const connections = [
            { from: [-8, 0.2, 2], to: [-4, 0.2, 2] }, // Electrode to INA128
            { from: [-4, 0.2, 2], to: [0, 0.2, 2] },  // INA128 to OPA2131
            { from: [0, 0.2, 2], to: [4, 0.2, 0] },   // OPA2131 to STM32
        ];
        
        connections.forEach(conn => {
            this.createWire(conn.from, conn.to);
        });
    }
    
    createWire(start, end) {
        const points = [
            new THREE.Vector3(start[0], start[1], start[2]),
            new THREE.Vector3(end[0], end[1], end[2])
        ];
        
        const geometry = new THREE.BufferGeometry().setFromPoints(points);
        const material = new THREE.LineBasicMaterial({ 
            color: this.colors.copper,
            linewidth: 3
        });
        
        const wire = new THREE.Line(geometry, material);
        this.scene.add(wire);
        this.connections.push(wire);
    }
    
    addComponentLabels() {
        // Create text labels for components
        const loader = new THREE.FontLoader();
        
        // For now, use simple text geometry or sprites
        this.components.forEach((component, name) => {
            this.createTextLabel(name, component.position);
        });
    }
    
    createTextLabel(text, position) {
        // Create a simple text sprite
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.width = 256;
        canvas.height = 64;
        
        context.fillStyle = '#ffffff';
        context.font = '20px Arial';
        context.textAlign = 'center';
        context.fillText(text, 128, 32);
        
        const texture = new THREE.CanvasTexture(canvas);
        const spriteMaterial = new THREE.SpriteMaterial({ map: texture });
        const sprite = new THREE.Sprite(spriteMaterial);
        
        sprite.position.set(position.x, position.y + 1, position.z);
        sprite.scale.set(2, 0.5, 1);
        
        this.scene.add(sprite);
    }
    
    startAnimation() {
        this.animate();
    }
    
    animate() {
        this.animationId = requestAnimationFrame(() => this.animate());
        
        if (this.controls) {
            this.controls.update();
        }
        
        // Rotate components slightly for visual interest
        this.components.forEach(component => {
            component.rotation.y += 0.001;
        });
        
        this.renderer.render(this.scene, this.camera);
    }
    
    handleResize() {
        const container = document.getElementById('threeDContainer');
        if (!container) return;
        
        const width = container.clientWidth;
        const height = container.clientHeight;
        
        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(width, height);
    }
    
    setView(viewType) {
        switch (viewType) {
            case 'front':
                this.camera.position.set(0, 5, 20);
                break;
            case 'side':
                this.camera.position.set(20, 5, 0);
                break;
            case 'top':
                this.camera.position.set(0, 20, 0);
                break;
            case 'isometric':
                this.camera.position.set(15, 10, 15);
                break;
        }
        
        this.camera.lookAt(0, 0, 0);
        
        if (this.controls) {
            this.controls.update();
        }
    }
    
    highlightComponent(componentName) {
        // Reset all components
        this.components.forEach(component => {
            component.material.emissive.setHex(0x000000);
        });
        
        // Highlight selected component
        const component = this.components.get(componentName);
        if (component) {
            component.material.emissive.setHex(0x444444);
        }
    }
    
    dispose() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        
        if (this.renderer) {
            this.renderer.dispose();
        }
        
        if (this.controls) {
            this.controls.dispose();
        }
    }
}

// Global functions for HTML onclick handlers
function set3DView(viewType) {
    if (window.threeDEngine) {
        window.threeDEngine.setView(viewType);
    }
}

function load3DView() {
    const container = document.getElementById('threeDContainer');
    if (container && typeof THREE !== 'undefined') {
        if (window.threeDEngine) {
            window.threeDEngine.dispose();
        }
        window.threeDEngine = new ThreeDDiagramEngine();
    } else {
        console.warn('Three.js not available or container not found');
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // 3D engine will be initialized when the 3D tab is activated
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThreeDDiagramEngine;
}
