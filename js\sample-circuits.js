// Sample ECG Circuit Loader
// Pre-designed ECG amplifier circuits for demonstration

class SampleCircuits {
    constructor() {
        this.circuits = {
            basicECGAmplifier: {
                name: "Basic ECG Amplifier",
                description: "Simple 3-lead ECG amplifier with INA128",
                components: [
                    {
                        id: "INA128_1",
                        type: "INA128UA",
                        x: 300,
                        y: 200,
                        rotation: 0,
                        value: "G=1100"
                    },
                    {
                        id: "R1",
                        type: "RESISTOR",
                        x: 200,
                        y: 150,
                        rotation: 0,
                        value: "47Ω"
                    },
                    {
                        id: "R2",
                        type: "RESISTOR",
                        x: 200,
                        y: 250,
                        rotation: 0,
                        value: "47Ω"
                    },
                    {
                        id: "C1",
                        type: "CAPACITOR",
                        x: 150,
                        y: 150,
                        rotation: 0,
                        value: "100nF"
                    },
                    {
                        id: "C2",
                        type: "CAPACITOR",
                        x: 150,
                        y: 250,
                        rotation: 0,
                        value: "100nF"
                    },
                    {
                        id: "C3",
                        type: "CAPACITOR",
                        x: 400,
                        y: 200,
                        rotation: 0,
                        value: "10µF"
                    },
                    {
                        id: "ELECTRODE_RA",
                        type: "ELECTRODE",
                        x: 50,
                        y: 150,
                        rotation: 0,
                        value: "RA"
                    },
                    {
                        id: "ELECTRODE_LA",
                        type: "ELECTRODE",
                        x: 50,
                        y: 250,
                        rotation: 0,
                        value: "LA"
                    },
                    {
                        id: "ELECTRODE_RL",
                        type: "ELECTRODE",
                        x: 50,
                        y: 350,
                        rotation: 0,
                        value: "RL"
                    },
                    {
                        id: "OSC1",
                        type: "OSCILLOSCOPE",
                        x: 500,
                        y: 150,
                        rotation: 0,
                        value: "CH1"
                    },
                    {
                        id: "DMM1",
                        type: "MULTIMETER",
                        x: 500,
                        y: 250,
                        rotation: 0,
                        value: "DC"
                    }
                ],
                connections: [
                    {
                        id: "wire1",
                        type: "wire",
                        startX: 70,
                        startY: 150,
                        endX: 130,
                        endY: 150,
                        waypoints: []
                    },
                    {
                        id: "wire2",
                        type: "wire",
                        startX: 70,
                        startY: 250,
                        endX: 130,
                        endY: 250,
                        waypoints: []
                    },
                    {
                        id: "wire3",
                        type: "wire",
                        startX: 170,
                        startY: 150,
                        endX: 220,
                        endY: 150,
                        waypoints: []
                    },
                    {
                        id: "wire4",
                        type: "wire",
                        startX: 170,
                        startY: 250,
                        endX: 220,
                        endY: 250,
                        waypoints: []
                    },
                    {
                        id: "wire5",
                        type: "wire",
                        startX: 220,
                        startY: 150,
                        endX: 275,
                        endY: 192,
                        waypoints: []
                    },
                    {
                        id: "wire6",
                        type: "wire",
                        startX: 220,
                        startY: 250,
                        endX: 275,
                        endY: 208,
                        waypoints: []
                    },
                    {
                        id: "wire7",
                        type: "wire",
                        startX: 325,
                        startY: 200,
                        endX: 380,
                        endY: 200,
                        waypoints: []
                    },
                    {
                        id: "wire8",
                        type: "wire",
                        startX: 420,
                        startY: 200,
                        endX: 470,
                        endY: 150,
                        waypoints: []
                    },
                    {
                        id: "wire9",
                        type: "wire",
                        startX: 420,
                        startY: 200,
                        endX: 475,
                        endY: 250,
                        waypoints: []
                    }
                ]
            },
            
            advancedECGSystem: {
                name: "Advanced ECG System",
                description: "Complete 12-lead ECG system with filtering",
                components: [
                    {
                        id: "INA128_1",
                        type: "INA128UA",
                        x: 200,
                        y: 150,
                        rotation: 0,
                        value: "G=500"
                    },
                    {
                        id: "OPA2131_1",
                        type: "OPA2131UA",
                        x: 350,
                        y: 150,
                        rotation: 0,
                        value: "G=2"
                    },
                    {
                        id: "STM32_1",
                        type: "STM32F4",
                        x: 500,
                        y: 200,
                        rotation: 0,
                        value: "MCU"
                    },
                    {
                        id: "R_GAIN",
                        type: "RESISTOR",
                        x: 150,
                        y: 100,
                        rotation: 0,
                        value: "100Ω"
                    },
                    {
                        id: "C_HPF",
                        type: "CAPACITOR",
                        x: 300,
                        y: 100,
                        rotation: 0,
                        value: "330nF"
                    },
                    {
                        id: "C_LPF",
                        type: "CAPACITOR",
                        x: 400,
                        y: 100,
                        rotation: 0,
                        value: "10nF"
                    },
                    {
                        id: "FGEN1",
                        type: "FUNCTION_GEN",
                        x: 50,
                        y: 150,
                        rotation: 0,
                        value: "ECG"
                    },
                    {
                        id: "OSC1",
                        type: "OSCILLOSCOPE",
                        x: 600,
                        y: 150,
                        rotation: 0,
                        value: "4CH"
                    }
                ],
                connections: [
                    {
                        id: "wire1",
                        type: "wire",
                        startX: 80,
                        startY: 150,
                        endX: 175,
                        endY: 142,
                        waypoints: []
                    },
                    {
                        id: "wire2",
                        type: "wire",
                        startX: 225,
                        startY: 150,
                        endX: 325,
                        endY: 142,
                        waypoints: []
                    },
                    {
                        id: "wire3",
                        type: "wire",
                        startX: 375,
                        startY: 150,
                        endX: 475,
                        endY: 180,
                        waypoints: []
                    },
                    {
                        id: "wire4",
                        type: "wire",
                        startX: 525,
                        startY: 180,
                        endX: 570,
                        endY: 150,
                        waypoints: []
                    }
                ]
            }
        };
    }
    
    loadCircuit(circuitName) {
        const circuit = this.circuits[circuitName];
        if (!circuit) {
            console.error(`Circuit "${circuitName}" not found`);
            return false;
        }
        
        try {
            // Clear existing circuit
            if (window.enhancedWorkbench) {
                window.enhancedWorkbench.components.clear();
                window.enhancedWorkbench.connections.clear();
                
                // Load components
                circuit.components.forEach(compData => {
                    const component = {
                        id: compData.id,
                        type: compData.type,
                        name: compData.type,
                        x: compData.x,
                        y: compData.y,
                        rotation: compData.rotation,
                        value: compData.value,
                        symbol: this.getComponentSymbol(compData.type),
                        color: this.getComponentColor(compData.type),
                        pins: this.getComponentPins(compData.type),
                        pinout: this.getComponentPinout(compData.type),
                        specs: this.getComponentSpecs(compData.type)
                    };
                    
                    window.enhancedWorkbench.components.set(component.id, component);
                });
                
                // Load connections
                circuit.connections.forEach(connData => {
                    const connection = {
                        id: connData.id,
                        type: connData.type,
                        startX: connData.startX,
                        startY: connData.startY,
                        endX: connData.endX,
                        endY: connData.endY,
                        waypoints: connData.waypoints || [],
                        color: '#333',
                        width: 2
                    };
                    
                    window.enhancedWorkbench.connections.set(connection.id, connection);
                });
                
                // Update displays
                window.enhancedWorkbench.updateComponentCount();
                window.enhancedWorkbench.updateConnectionCount();
                window.enhancedWorkbench.redraw();
                
                // Show success notification
                if (typeof showNotification === 'function') {
                    showNotification(`Loaded "${circuit.name}" successfully!`, 'success');
                }
                
                console.log(`✅ Loaded circuit: ${circuit.name}`);
                return true;
            }
        } catch (error) {
            console.error('Error loading circuit:', error);
            if (typeof showNotification === 'function') {
                showNotification('Failed to load circuit', 'error');
            }
            return false;
        }
    }
    
    getComponentSymbol(type) {
        const symbolMap = {
            'INA128UA': 'triangle',
            'OPA2131UA': 'triangle',
            'LM358': 'triangle',
            'OP07D': 'triangle',
            'RESISTOR': 'rectangle',
            'CAPACITOR': 'capacitor',
            'INDUCTOR': 'inductor',
            'A0509S': 'rectangle',
            'LM7805': 'rectangle',
            'ELECTRODE': 'circle',
            'CONNECTOR_2': 'connector',
            'STM32F4': 'rectangle',
            'ARDUINO_UNO': 'rectangle',
            'OSCILLOSCOPE': 'oscilloscope',
            'MULTIMETER': 'multimeter',
            'FUNCTION_GEN': 'function_gen'
        };
        return symbolMap[type] || 'rectangle';
    }
    
    getComponentColor(type) {
        const colorMap = {
            'INA128UA': '#4CAF50',
            'OPA2131UA': '#2196F3',
            'LM358': '#FF9800',
            'OP07D': '#E91E63',
            'RESISTOR': '#795548',
            'CAPACITOR': '#607D8B',
            'INDUCTOR': '#9C27B0',
            'A0509S': '#FFC107',
            'LM7805': '#FF5722',
            'ELECTRODE': '#FF6B6B',
            'CONNECTOR_2': '#9E9E9E',
            'STM32F4': '#3F51B5',
            'ARDUINO_UNO': '#00BCD4',
            'OSCILLOSCOPE': '#4CAF50',
            'MULTIMETER': '#FF9800',
            'FUNCTION_GEN': '#2196F3'
        };
        return colorMap[type] || '#9E9E9E';
    }
    
    getComponentPins(type) {
        const pinMap = {
            'INA128UA': 8,
            'OPA2131UA': 8,
            'LM358': 8,
            'OP07D': 8,
            'RESISTOR': 2,
            'CAPACITOR': 2,
            'INDUCTOR': 2,
            'A0509S': 4,
            'LM7805': 3,
            'ELECTRODE': 1,
            'CONNECTOR_2': 2,
            'STM32F4': 100,
            'ARDUINO_UNO': 30,
            'OSCILLOSCOPE': 4,
            'MULTIMETER': 3,
            'FUNCTION_GEN': 2
        };
        return pinMap[type] || 2;
    }
    
    getComponentPinout(type) {
        const pinoutMap = {
            'INA128UA': ['RG', 'IN-', 'IN+', 'V-', 'REF', 'OUT', 'V+', 'RG'],
            'OPA2131UA': ['OUT1', 'IN1-', 'IN1+', 'V-', 'IN2+', 'IN2-', 'OUT2', 'V+'],
            'RESISTOR': ['1', '2'],
            'CAPACITOR': ['+', '-'],
            'ELECTRODE': ['SIGNAL'],
            'OSCILLOSCOPE': ['CH1', 'CH2', 'GND', 'TRIG'],
            'MULTIMETER': ['V/Ω', 'COM', 'A'],
            'FUNCTION_GEN': ['OUT', 'GND']
        };
        return pinoutMap[type] || ['1', '2'];
    }
    
    getComponentSpecs(type) {
        // Return basic specs - full specs are in the enhanced workbench
        return { value: '1k', tolerance: '±5%' };
    }
    
    getAvailableCircuits() {
        return Object.keys(this.circuits).map(key => ({
            id: key,
            name: this.circuits[key].name,
            description: this.circuits[key].description
        }));
    }
}

// Global instance
const sampleCircuits = new SampleCircuits();

// Global function for HTML onclick
function loadSampleProject() {
    const circuits = sampleCircuits.getAvailableCircuits();
    
    // Create selection dialog
    const circuitOptions = circuits.map(circuit => 
        `<option value="${circuit.id}">${circuit.name} - ${circuit.description}</option>`
    ).join('');
    
    const html = `
        <div style="padding: 20px;">
            <h3>Select Sample Circuit</h3>
            <select id="circuitSelect" style="width: 100%; padding: 8px; margin: 10px 0;">
                ${circuitOptions}
            </select>
            <div style="margin-top: 15px;">
                <button onclick="loadSelectedCircuit()" style="padding: 8px 16px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">Load Circuit</button>
                <button onclick="closeCircuitDialog()" style="padding: 8px 16px; background: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer; margin-left: 8px;">Cancel</button>
            </div>
        </div>
    `;
    
    // Show in modal or alert
    if (typeof showNotification === 'function') {
        showNotification('Sample circuits available in console. Use: sampleCircuits.loadCircuit("basicECGAmplifier")', 'info');
    }
    
    // For now, just load the basic circuit
    sampleCircuits.loadCircuit('basicECGAmplifier');
}

function loadSelectedCircuit() {
    const select = document.getElementById('circuitSelect');
    if (select) {
        sampleCircuits.loadCircuit(select.value);
    }
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SampleCircuits;
}
